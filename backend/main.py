#!/usr/bin/env python3
"""
RAG系统主入口
启动RAG检索增强生成服务
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 延迟导入以避免循环依赖
def get_app_dependencies():
    """延迟导入应用依赖"""
    try:
        from config import get_settings
        from utils.logger import setup_logger
        return get_settings(), setup_logger
    except ImportError as e:
        print(f"导入依赖失败: {e}")
        # 返回默认值
        from types import SimpleNamespace
        default_settings = SimpleNamespace(
            log_level="INFO",
            debug=False,
            host="0.0.0.0",
            port=8000
        )
        def default_logger(name, level=None):
            import logging
            return logging.getLogger(name)
        return default_settings, default_logger

# 初始化配置和日志
settings, setup_logger = get_app_dependencies()
logger = setup_logger(
    name="rag-system",
    level=getattr(settings, 'log_level', 'INFO'),
    log_file=settings.log_file
)

# 创建FastAPI应用
app = FastAPI(
    title="RAG System",
    description="检索增强生成系统 - 为AI合同审查提供智能检索和知识增强",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 启动RAG系统...")

    try:
        # 延迟导入RAG控制器以避免循环依赖
        try:
            from core.controller import RAGController
            from models.config import RAGConfig

            # 创建配置
            config = RAGConfig(
                debug_mode=getattr(settings, 'debug', False),
                enable_cache=True,
                log_level=getattr(settings, 'log_level', 'INFO')
            )

            # 初始化RAG控制器
            rag_controller = RAGController(config)

            # 初始化所有组件
            init_success = await rag_controller.initialize()
        except ImportError as e:
            logger.warning(f"RAG控制器导入失败，使用模拟模式: {e}")
            init_success = True  # 在测试模式下继续
        if not init_success:
            logger.warning("⚠️ RAG系统部分组件初始化失败")

        # 执行健康检查
        health_status = await rag_controller.health_check()
        if health_status.get('status') != 'healthy':
            logger.warning(f"⚠️ RAG系统组件状态异常: {health_status}")

        # 将控制器实例存储到应用状态中
        app.state.rag_controller = rag_controller

        logger.info("✅ RAG系统启动成功")

    except Exception as e:
        logger.error(f"❌ RAG系统启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 关闭RAG系统...")

    try:
        # 关闭RAG控制器和所有组件
        if hasattr(app.state, 'rag_controller'):
            await app.state.rag_controller.shutdown()

        logger.info("✅ RAG系统关闭完成")

    except Exception as e:
        logger.error(f"❌ RAG系统关闭异常: {e}")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc) if settings.debug else "系统异常，请稍后重试",
            "type": type(exc).__name__
        }
    )


@app.get("/")
async def root():
    """根路径 - 系统信息"""
    return {
        "name": "RAG System",
        "version": "1.0.0",
        "description": "检索增强生成系统",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "docs": "/docs" if settings.debug else None
    }


# 注册API路由（延迟导入以避免循环依赖）
try:
    from api.routes import router as api_router
    app.include_router(api_router, prefix="/api/v1", tags=["RAG API"])
except ImportError as e:
    logger.warning(f"API路由导入失败，跳过路由注册: {e}")
    # 在测试模式下，我们可能不需要完整的API路由


def create_app() -> FastAPI:
    """创建应用实例（用于测试）"""
    return app


async def main():
    """主函数"""
    logger.info("🔧 配置RAG系统...")

    # 验证配置
    if not settings.validate():
        logger.error("❌ 配置验证失败")
        sys.exit(1)

    logger.info("✅ 配置验证通过")

    # 启动服务器
    config = uvicorn.Config(
        app=app,
        host=settings.host,
        port=settings.port,
        workers=1,  # RAG系统使用单进程多协程
        log_level=settings.log_level.lower(),
        reload=settings.debug,
        access_log=settings.debug
    )

    server = uvicorn.Server(config)

    try:
        logger.info(f"🌐 RAG系统启动在 http://{settings.host}:{settings.port}")
        logger.info(f"📚 API文档地址: http://{settings.host}:{settings.port}/docs")
        await server.serve()
    except KeyboardInterrupt:
        logger.info("👋 收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"❌ 服务器运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)

    # 创建必要的目录
    directories = [
        "./logs",
        "./data",
        "./data/uploads",
        "./data/knowledge_base",
        "./data/indexes",
        "./data/embeddings",
        "./data/cache"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 RAG系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
