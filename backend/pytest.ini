[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 最小版本要求
minversion = 7.0

# 默认选项
addopts =
    --strict-markers
    --strict-config
    --tb=short
    --color=yes
    -ra
    -p no:warnings

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试（性能测试等）
    api: API测试
    cache: 缓存相关测试
    database: 数据库相关测试
    ai_model: AI模型相关测试

# 异步测试配置
asyncio_mode = auto

# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Python路径配置
pythonpath = .

# 覆盖率配置
[coverage:run]
source = core, models, utils
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2

[coverage:html]
directory = htmlcov
