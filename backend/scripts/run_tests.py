#!/usr/bin/env python3
"""
RAG系统测试运行脚本
提供便捷的测试执行和报告功能
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_command(command, description=""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    if description:
        print(f"🔄 {description}")
    print(f"命令: {' '.join(command)}")
    print('='*60)
    
    try:
        result = subprocess.run(
            command,
            cwd=project_root,
            capture_output=False,
            text=True,
            check=False
        )
        
        if result.returncode == 0:
            print(f"✅ {description or '命令'} 执行成功")
            return True
        else:
            print(f"❌ {description or '命令'} 执行失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False


def install_test_dependencies():
    """安装测试依赖"""
    print("📦 安装测试依赖...")
    
    test_requirements = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-xdist>=3.0.0",  # 并行测试
        "pytest-html>=3.1.0",  # HTML报告
        "httpx>=0.24.0",  # API测试
        "fastapi[all]>=0.100.0"  # FastAPI测试
    ]
    
    for requirement in test_requirements:
        command = [sys.executable, "-m", "pip", "install", requirement]
        success = run_command(command, f"安装 {requirement}")
        if not success:
            print(f"⚠️ 安装 {requirement} 失败，继续安装其他依赖")
    
    return True


def run_unit_tests(coverage=False, verbose=False):
    """运行单元测试"""
    command = [sys.executable, "-m", "pytest", "tests/unit/"]
    
    if coverage:
        command.extend([
            "--cov=core",
            "--cov=models", 
            "--cov=utils",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing"
        ])
    
    if verbose:
        command.append("-v")
    
    command.extend([
        "--tb=short",
        "-m", "unit"
    ])
    
    return run_command(command, "运行单元测试")


def run_integration_tests(verbose=False):
    """运行集成测试"""
    command = [sys.executable, "-m", "pytest", "tests/integration/"]
    
    if verbose:
        command.append("-v")
    
    command.extend([
        "--tb=short",
        "-m", "integration"
    ])
    
    return run_command(command, "运行集成测试")


def run_all_tests(coverage=False, verbose=False, parallel=False):
    """运行所有测试"""
    command = [sys.executable, "-m", "pytest", "tests/"]
    
    if coverage:
        command.extend([
            "--cov=core",
            "--cov=models",
            "--cov=utils", 
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    if verbose:
        command.append("-v")
    
    if parallel:
        command.extend(["-n", "auto"])  # 自动检测CPU核心数
    
    command.extend([
        "--tb=short",
        "--html=test_report.html",
        "--self-contained-html"
    ])
    
    return run_command(command, "运行所有测试")


def run_specific_test(test_path, verbose=False):
    """运行特定测试"""
    command = [sys.executable, "-m", "pytest", test_path]
    
    if verbose:
        command.append("-v")
    
    command.extend(["--tb=short"])
    
    return run_command(command, f"运行测试: {test_path}")


def run_performance_tests():
    """运行性能测试"""
    command = [
        sys.executable, "-m", "pytest", 
        "tests/", 
        "-m", "slow",
        "-v",
        "--tb=short"
    ]
    
    return run_command(command, "运行性能测试")


def lint_code():
    """代码质量检查"""
    print("🔍 运行代码质量检查...")
    
    # 尝试运行flake8
    flake8_command = [sys.executable, "-m", "flake8", "core/", "models/", "utils/", "tests/"]
    flake8_success = run_command(flake8_command, "Flake8 代码检查")
    
    # 尝试运行black检查
    black_command = [sys.executable, "-m", "black", "--check", "core/", "models/", "utils/", "tests/"]
    black_success = run_command(black_command, "Black 代码格式检查")
    
    return flake8_success and black_success


def generate_test_report():
    """生成测试报告"""
    print("📊 生成测试报告...")
    
    # 运行测试并生成报告
    command = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--html=test_report.html",
        "--self-contained-html",
        "--cov=core",
        "--cov=models",
        "--cov=utils",
        "--cov-report=html:htmlcov",
        "--cov-report=xml",
        "--junit-xml=test_results.xml"
    ]
    
    success = run_command(command, "生成测试报告")
    
    if success:
        print("\n📋 测试报告已生成:")
        print("  - HTML报告: test_report.html")
        print("  - 覆盖率报告: htmlcov/index.html")
        print("  - JUnit XML: test_results.xml")
        print("  - 覆盖率XML: coverage.xml")
    
    return success


def clean_test_artifacts():
    """清理测试产物"""
    print("🧹 清理测试产物...")
    
    artifacts = [
        "test_report.html",
        "test_results.xml", 
        "coverage.xml",
        "htmlcov/",
        ".coverage",
        ".pytest_cache/",
        "__pycache__/",
        "*.pyc"
    ]
    
    for artifact in artifacts:
        artifact_path = project_root / artifact
        if artifact_path.exists():
            if artifact_path.is_dir():
                import shutil
                shutil.rmtree(artifact_path)
                print(f"  删除目录: {artifact}")
            else:
                artifact_path.unlink()
                print(f"  删除文件: {artifact}")
    
    print("✅ 清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="RAG系统测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_tests.py --all                    # 运行所有测试
  python run_tests.py --unit --coverage        # 运行单元测试并生成覆盖率
  python run_tests.py --integration -v         # 运行集成测试（详细输出）
  python run_tests.py --install-deps           # 安装测试依赖
  python run_tests.py --lint                   # 代码质量检查
  python run_tests.py --report                 # 生成完整测试报告
  python run_tests.py --clean                  # 清理测试产物
        """
    )
    
    # 测试类型选项
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument("--unit", action="store_true", help="运行单元测试")
    test_group.add_argument("--integration", action="store_true", help="运行集成测试")
    test_group.add_argument("--all", action="store_true", help="运行所有测试")
    test_group.add_argument("--performance", action="store_true", help="运行性能测试")
    test_group.add_argument("--test", type=str, help="运行特定测试文件或目录")
    
    # 测试选项
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    parser.add_argument("--parallel", action="store_true", help="并行运行测试")
    
    # 工具选项
    parser.add_argument("--install-deps", action="store_true", help="安装测试依赖")
    parser.add_argument("--lint", action="store_true", help="运行代码质量检查")
    parser.add_argument("--report", action="store_true", help="生成完整测试报告")
    parser.add_argument("--clean", action="store_true", help="清理测试产物")
    
    args = parser.parse_args()
    
    # 如果没有指定任何选项，显示帮助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    success = True
    
    try:
        if args.install_deps:
            success &= install_test_dependencies()
        
        if args.clean:
            clean_test_artifacts()
        
        if args.lint:
            success &= lint_code()
        
        if args.unit:
            success &= run_unit_tests(args.coverage, args.verbose)
        
        if args.integration:
            success &= run_integration_tests(args.verbose)
        
        if args.all:
            success &= run_all_tests(args.coverage, args.verbose, args.parallel)
        
        if args.performance:
            success &= run_performance_tests()
        
        if args.test:
            success &= run_specific_test(args.test, args.verbose)
        
        if args.report:
            success &= generate_test_report()
        
        # 输出总结
        print("\n" + "="*60)
        if success:
            print("🎉 所有操作完成成功！")
            sys.exit(0)
        else:
            print("⚠️ 部分操作失败，请检查上面的输出")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 操作被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
