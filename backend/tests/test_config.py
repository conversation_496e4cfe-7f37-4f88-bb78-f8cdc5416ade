"""
测试专用配置
避免循环依赖和复杂的初始化
"""

import os
import sys
from pathlib import Path
from unittest.mock import MagicMock

# 设置测试环境
os.environ['TESTING'] = 'true'
os.environ['PYTHONPATH'] = str(Path(__file__).parent.parent)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


def setup_test_environment():
    """设置测试环境"""
    # 模拟一些可能导致循环依赖的模块
    
    # 如果导入失败，创建模拟对象
    try:
        from models.config import RAGConfig
    except ImportError:
        class RAGConfig:
            def __init__(self, **kwargs):
                self.enable_cache = kwargs.get('enable_cache', True)
                self.cache_ttl = kwargs.get('cache_ttl', 300)
                self.max_context_length = kwargs.get('max_context_length', 2000)
                self.debug_mode = kwargs.get('debug_mode', True)
                self.enable_monitoring = kwargs.get('enable_monitoring', False)
        
        # 将模拟类添加到sys.modules
        import types
        mock_module = types.ModuleType('models.config')
        mock_module.RAGConfig = RAGConfig
        sys.modules['models.config'] = mock_module
    
    try:
        from config.settings import get_settings
    except ImportError:
        def get_settings():
            from types import SimpleNamespace
            return SimpleNamespace(
                debug=True,
                log_level="INFO",
                host="0.0.0.0",
                port=8000,
                postgres_url="postgresql://localhost:5432/test_db",
                redis_url="redis://localhost:6379/15",
                openai_api_key=None,
                qwen_api_key=None,
                jwt_secret="test-secret"
            )
        
        # 将模拟函数添加到sys.modules
        import types
        mock_module = types.ModuleType('config.settings')
        mock_module.get_settings = get_settings
        sys.modules['config.settings'] = mock_module
    
    try:
        from utils.logger import get_logger
    except ImportError:
        def get_logger(name):
            import logging
            logging.basicConfig(level=logging.INFO)
            return logging.getLogger(name)
        
        # 将模拟函数添加到sys.modules
        import types
        mock_module = types.ModuleType('utils.logger')
        mock_module.get_logger = get_logger
        sys.modules['utils.logger'] = mock_module


def create_mock_app():
    """创建模拟的FastAPI应用"""
    try:
        from fastapi import FastAPI
        app = FastAPI(title="RAG System Test", version="1.0.0")
        
        @app.get("/")
        async def root():
            return {"message": "RAG System Test", "version": "1.0.0"}
        
        @app.get("/health")
        async def health():
            return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}
        
        return app
    except ImportError:
        # 如果FastAPI不可用，返回模拟对象
        return MagicMock()


# 在导入时自动设置测试环境
if os.environ.get('TESTING') == 'true':
    setup_test_environment()


# 导出测试工具
__all__ = ['setup_test_environment', 'create_mock_app']
