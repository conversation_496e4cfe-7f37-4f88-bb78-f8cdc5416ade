"""
缓存系统单元测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import pytest
import pytest_asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from core.storage.cache_store import CacheManager
from core.cache.query_cache import QueryCacheManager, CacheEntry, CacheStats
from core.cache.session_cache import SessionCacheManager, SessionContext
from models.query import QueryRequest
from models.response import RAGResponse


@pytest.mark.unit
@pytest.mark.asyncio
class TestCacheManager:
    """缓存管理器测试类"""
    
    async def test_initialization(self, mock_cache_manager):
        """测试初始化"""
        assert mock_cache_manager is not None
        assert mock_cache_manager.connected is True
    
    async def test_basic_cache_operations(self, mock_cache_manager):
        """测试基本缓存操作"""
        key = "test_key"
        value = {"test": "value", "number": 123}
        
        # 测试设置缓存
        success = await mock_cache_manager.set(key, value, 60)
        assert success is True
        
        # 测试获取缓存
        mock_cache_manager.get.return_value = value
        retrieved = await mock_cache_manager.get(key)
        assert retrieved == value
        
        # 测试检查存在
        mock_cache_manager.exists.return_value = True
        exists = await mock_cache_manager.exists(key)
        assert exists is True
        
        # 测试删除缓存
        success = await mock_cache_manager.delete(key)
        assert success is True
    
    async def test_hash_operations(self, mock_cache_manager):
        """测试哈希操作"""
        key = "test_hash"
        field = "test_field"
        value = "test_value"
        
        # 测试设置哈希字段
        mock_cache_manager.set_hash = AsyncMock(return_value=True)
        success = await mock_cache_manager.set_hash(key, field, value, 60)
        assert success is True
        
        # 测试获取哈希字段
        mock_cache_manager.get_hash = AsyncMock(return_value=value)
        retrieved = await mock_cache_manager.get_hash(key, field)
        assert retrieved == value
    
    async def test_list_operations(self, mock_cache_manager):
        """测试列表操作"""
        key = "test_list"
        values = ["item1", "item2", {"item": 3}]
        
        # 测试推送列表
        mock_cache_manager.push_list = AsyncMock(return_value=len(values))
        count = await mock_cache_manager.push_list(key, *values, ttl=60)
        assert count == len(values)
        
        # 测试获取列表
        mock_cache_manager.get_list = AsyncMock(return_value=values)
        retrieved = await mock_cache_manager.get_list(key, 0, -1)
        assert retrieved == values
    
    async def test_counter_operations(self, mock_cache_manager):
        """测试计数器操作"""
        key = "test_counter"
        
        # 测试递增
        mock_cache_manager.increment = AsyncMock(side_effect=[1, 6])
        
        count1 = await mock_cache_manager.increment(key, 1, 60)
        assert count1 == 1
        
        count2 = await mock_cache_manager.increment(key, 5, 60)
        assert count2 == 6
    
    async def test_pattern_clearing(self, mock_cache_manager):
        """测试模式清理"""
        pattern = "test:*"
        
        mock_cache_manager.clear_pattern = AsyncMock(return_value=5)
        count = await mock_cache_manager.clear_pattern(pattern)
        assert count == 5
    
    async def test_health_check(self, mock_cache_manager):
        """测试健康检查"""
        health = await mock_cache_manager.health_check()
        assert health is True
    
    async def test_statistics(self, mock_cache_manager):
        """测试统计信息"""
        stats = await mock_cache_manager.get_statistics()
        assert isinstance(stats, dict)
        assert 'cache_type' in stats


@pytest.mark.unit
@pytest.mark.asyncio
class TestQueryCacheManager:
    """查询缓存管理器测试类"""
    
    @pytest_asyncio.fixture
    async def query_cache(self, mock_cache_manager):
        """查询缓存夹具"""
        try:
            from core.cache.query_cache import QueryCacheManager
            return QueryCacheManager(mock_cache_manager)
        except ImportError:
            # 如果导入失败，返回Mock对象
            from unittest.mock import MagicMock, AsyncMock
            cache = MagicMock()
            cache.cache_manager = mock_cache_manager
            cache.stats = MagicMock()
            cache.get_cached_response = AsyncMock(return_value=None)
            cache.cache_response = AsyncMock(return_value=True)
            cache.invalidate_cache = AsyncMock(return_value=0)
            cache.get_similar_queries = AsyncMock(return_value=[])
            cache.preload_cache = AsyncMock(return_value=0)
            cache.get_cache_statistics = AsyncMock(return_value={})
            cache._generate_query_hash = MagicMock(return_value="test_hash")
            return cache
    
    async def test_initialization(self, query_cache):
        """测试初始化"""
        assert query_cache is not None
        assert query_cache.cache_manager is not None
        assert query_cache.stats is not None
    
    async def test_cache_miss(self, query_cache, sample_query_request):
        """测试缓存未命中"""
        # 模拟缓存未命中
        query_cache.cache_manager.get = AsyncMock(return_value=None)
        
        response = await query_cache.get_cached_response(sample_query_request)
        assert response is None
        assert query_cache.stats.miss_count == 1
    
    async def test_cache_hit(self, query_cache, sample_query_request, sample_rag_response):
        """测试缓存命中"""
        # 创建缓存条目
        cache_entry = CacheEntry(
            query_hash="test_hash",
            query_text=sample_query_request.query,
            response=sample_rag_response,
            created_at=datetime.utcnow()
        )
        
        # 模拟缓存命中
        cached_data = {
            'query_hash': cache_entry.query_hash,
            'query_text': cache_entry.query_text,
            'response': {
                'response': sample_rag_response.response,
                'sources': sample_rag_response.sources,
                'confidence': sample_rag_response.confidence,
                'context': sample_rag_response.context,
                'metadata': sample_rag_response.metadata
            },
            'created_at': cache_entry.created_at.isoformat(),
            'access_count': 0,
            'last_accessed': None
        }
        
        query_cache.cache_manager.get = AsyncMock(return_value=cached_data)
        query_cache._update_access_stats = AsyncMock()
        
        response = await query_cache.get_cached_response(sample_query_request)
        
        assert response is not None
        assert response.response == sample_rag_response.response
        assert query_cache.stats.hit_count == 1
    
    async def test_cache_response(self, query_cache, sample_query_request, sample_rag_response):
        """测试缓存响应"""
        # 模拟缓存操作
        query_cache.cache_manager.set = AsyncMock(return_value=True)
        query_cache._determine_ttl = AsyncMock(return_value=3600)
        query_cache._update_cache_stats = AsyncMock()
        query_cache._check_hot_query = AsyncMock()
        
        success = await query_cache.cache_response(sample_query_request, sample_rag_response)
        assert success is True
        
        # 验证调用
        query_cache.cache_manager.set.assert_called_once()
        query_cache._update_cache_stats.assert_called_once()
        query_cache._check_hot_query.assert_called_once()
    
    async def test_invalidate_cache(self, query_cache):
        """测试缓存失效"""
        query_cache.cache_manager.clear_pattern = AsyncMock(return_value=10)
        
        count = await query_cache.invalidate_cache()
        assert count == 10
        assert query_cache.stats.eviction_count == 10
    
    async def test_similar_queries(self, query_cache):
        """测试相似查询"""
        # 模拟热点查询数据
        hot_queries = [
            {'query': '什么是人工智能技术'},
            {'query': '人工智能的应用领域'},
            {'query': '机器学习算法'}
        ]
        
        query_cache.cache_manager.get_list = AsyncMock(return_value=hot_queries)
        
        similar = await query_cache.get_similar_queries("人工智能", limit=3)
        assert isinstance(similar, list)
    
    async def test_preload_cache(self, query_cache):
        """测试预加载缓存"""
        queries = ["查询1", "查询2", "查询3"]
        
        query_cache.cache_manager.set = AsyncMock(return_value=True)
        
        count = await query_cache.preload_cache(queries)
        assert count == len(queries)
    
    async def test_cache_statistics(self, query_cache):
        """测试缓存统计"""
        query_cache.cache_manager.get_statistics = AsyncMock(return_value={
            'cache_type': 'memory',
            'total_keys': 100,
            'memory_usage': 1024
        })
        query_cache.cache_manager.get_list = AsyncMock(return_value=[])
        
        stats = await query_cache.get_cache_statistics()
        
        assert isinstance(stats, dict)
        assert 'hit_rate' in stats
        assert 'cache_type' in stats
    
    async def test_query_hash_generation(self, query_cache, sample_query_request):
        """测试查询哈希生成"""
        hash1 = query_cache._generate_query_hash(sample_query_request)
        hash2 = query_cache._generate_query_hash(sample_query_request)
        
        assert hash1 == hash2  # 相同请求应生成相同哈希
        assert isinstance(hash1, str)
        assert len(hash1) == 32  # MD5哈希长度


@pytest.mark.unit
@pytest.mark.asyncio
class TestSessionCacheManager:
    """会话缓存管理器测试类"""
    
    @pytest_asyncio.fixture
    async def session_cache(self, mock_cache_manager):
        """会话缓存夹具"""
        try:
            from core.cache.session_cache import SessionCacheManager
            return SessionCacheManager(mock_cache_manager)
        except ImportError:
            # 如果导入失败，返回Mock对象
            from unittest.mock import MagicMock, AsyncMock
            cache = MagicMock()
            cache.cache_manager = mock_cache_manager
            cache.create_session = AsyncMock(return_value="test_session_id")
            cache.get_session = AsyncMock(return_value=None)
            cache.update_session = AsyncMock(return_value=True)
            cache.delete_session = AsyncMock(return_value=True)
            cache.add_conversation_turn = AsyncMock(return_value=True)
            cache.get_conversation_history = AsyncMock(return_value=[])
            cache.cleanup_expired_sessions = AsyncMock(return_value=0)
            cache.get_session_statistics = AsyncMock(return_value={})
            cache._add_user_session = AsyncMock()
            cache._remove_user_session = AsyncMock()
            cache._update_context_summary = AsyncMock()
            return cache
    
    async def test_initialization(self, session_cache):
        """测试初始化"""
        assert session_cache is not None
        assert session_cache.cache_manager is not None
    
    async def test_create_session(self, session_cache):
        """测试创建会话"""
        user_id = "test_user"
        metadata = {"client": "test"}
        
        session_cache.cache_manager.set = AsyncMock(return_value=True)
        session_cache._add_user_session = AsyncMock()
        
        session_id = await session_cache.create_session(user_id, metadata)
        
        assert session_id is not None
        assert isinstance(session_id, str)
        
        # 验证调用
        session_cache.cache_manager.set.assert_called_once()
        session_cache._add_user_session.assert_called_once_with(user_id, session_id)
    
    async def test_get_session(self, session_cache):
        """测试获取会话"""
        session_id = "test_session"
        session_data = {
            'session_id': session_id,
            'user_id': 'test_user',
            'conversation_history': [],
            'context_summary': '',
            'created_at': datetime.utcnow().isoformat(),
            'last_active': datetime.utcnow().isoformat(),
            'metadata': {}
        }
        
        session_cache.cache_manager.get = AsyncMock(return_value=session_data)
        
        session_context = await session_cache.get_session(session_id)
        
        assert session_context is not None
        assert session_context.session_id == session_id
        assert session_context.user_id == 'test_user'
    
    async def test_add_conversation_turn(self, session_cache):
        """测试添加对话轮次"""
        session_id = "test_session"
        query = "测试查询"
        response = "测试响应"
        confidence = 0.8
        sources = [{"source": "test"}]
        
        # 模拟会话存在
        session_context = SessionContext(
            session_id=session_id,
            user_id="test_user",
            conversation_history=[],
            context_summary="",
            created_at=datetime.utcnow(),
            last_active=datetime.utcnow(),
            metadata={}
        )
        
        session_cache.get_session = AsyncMock(return_value=session_context)
        session_cache.update_session = AsyncMock(return_value=True)
        session_cache.cache_manager.set = AsyncMock(return_value=True)
        session_cache._update_context_summary = AsyncMock()
        
        success = await session_cache.add_conversation_turn(
            session_id, query, response, confidence, sources
        )
        
        assert success is True
        assert len(session_context.conversation_history) == 1
        
        # 验证调用
        session_cache.update_session.assert_called_once()
        session_cache.cache_manager.set.assert_called_once()
    
    async def test_get_conversation_history(self, session_cache):
        """测试获取对话历史"""
        session_id = "test_session"
        
        # 创建带历史的会话
        conversation_data = {
            'turn_id': 'turn_1',
            'query': '测试查询',
            'response': '测试响应',
            'timestamp': datetime.utcnow().isoformat(),
            'confidence': 0.8,
            'sources': []
        }
        
        session_context = SessionContext(
            session_id=session_id,
            user_id="test_user",
            conversation_history=[conversation_data],
            context_summary="",
            created_at=datetime.utcnow(),
            last_active=datetime.utcnow(),
            metadata={}
        )
        
        session_cache.get_session = AsyncMock(return_value=session_context)
        
        history = await session_cache.get_conversation_history(session_id)
        
        assert len(history) == 1
        assert history[0].query == '测试查询'
        assert history[0].response == '测试响应'
    
    async def test_delete_session(self, session_cache):
        """测试删除会话"""
        session_id = "test_session"
        
        # 模拟会话存在
        session_context = SessionContext(
            session_id=session_id,
            user_id="test_user",
            conversation_history=[
                {
                    'turn_id': 'turn_1',
                    'query': '测试',
                    'response': '响应',
                    'timestamp': datetime.utcnow().isoformat(),
                    'confidence': 0.8,
                    'sources': []
                }
            ],
            context_summary="",
            created_at=datetime.utcnow(),
            last_active=datetime.utcnow(),
            metadata={}
        )
        
        session_cache.get_session = AsyncMock(return_value=session_context)
        session_cache.cache_manager.delete = AsyncMock(return_value=True)
        session_cache._remove_user_session = AsyncMock()
        
        success = await session_cache.delete_session(session_id)
        
        assert success is True
        
        # 验证删除调用
        assert session_cache.cache_manager.delete.call_count >= 2  # 会话和对话轮次
        session_cache._remove_user_session.assert_called_once()
    
    async def test_cleanup_expired_sessions(self, session_cache):
        """测试清理过期会话"""
        session_cache.cache_manager.clear_pattern = AsyncMock(return_value=5)
        
        count = await session_cache.cleanup_expired_sessions()
        assert count == 5
    
    async def test_session_statistics(self, session_cache):
        """测试会话统计"""
        stats = await session_cache.get_session_statistics()
        
        assert isinstance(stats, dict)
        assert 'session_ttl' in stats
        assert 'conversation_ttl' in stats
