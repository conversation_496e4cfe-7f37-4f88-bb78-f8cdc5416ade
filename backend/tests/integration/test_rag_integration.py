"""
RAG系统集成测试
测试各组件之间的协作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import pytest
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, patch

from core.controller import <PERSON>GController, RAGConfig
from core.knowledge import KnowledgeManager
from core.pipeline import RAGPipeline
from models.query import QueryRequest, QueryType
from models.response import RAGResponse


@pytest.mark.integration
@pytest.mark.asyncio
class TestRAGIntegration:
    """RAG系统集成测试类"""
    
    @pytest_asyncio.fixture
    async def integration_controller(self):
        """集成测试控制器"""
        controller = RAGController(test_config)
        
        # 使用真实组件但模拟外部依赖
        with patch('core.storage.database.ASYNCPG_AVAILABLE', False), \
             patch('core.storage.vector_store.CHROMADB_AVAILABLE', False), \
             patch('core.storage.cache_store.REDIS_AVAILABLE', False):
            
            await controller.initialize()
            yield controller
            await controller.shutdown()
    
    async def test_end_to_end_query_flow(self, integration_controller, sample_query_request):
        """测试端到端查询流程"""
        # 执行查询
        response = await integration_controller.query(sample_query_request)
        
        # 验证响应
        assert response is not None
        assert response.success is True
        assert len(response.response) > 0
        assert response.query_id is not None
        assert response.processing_time > 0
        
        # 验证统计更新
        assert integration_controller.metrics['total_queries'] == 1
    
    async def test_document_upload_and_query_flow(self, integration_controller, temp_dir):
        """测试文档上传和查询流程"""
        # 创建测试文档
        test_file = temp_dir / "integration_test.txt"
        test_content = """
        人工智能技术概述
        
        人工智能（AI）是计算机科学的一个重要分支，它研究如何让机器模拟人类的智能行为。
        主要包括机器学习、深度学习、自然语言处理等技术。
        
        机器学习是AI的核心技术之一，通过算法让计算机从数据中学习模式。
        深度学习则是机器学习的一个子领域，使用神经网络进行复杂的模式识别。
        """
        test_file.write_text(test_content, encoding='utf-8')
        
        # 上传文档
        upload_success = await integration_controller.add_document(
            str(test_file),
            metadata={'test': True, 'topic': 'AI'}
        )
        assert upload_success is True
        
        # 等待处理完成
        await asyncio.sleep(0.1)
        
        # 查询相关内容
        query_request = QueryRequest(
            query="什么是机器学习？",
            query_type=QueryType.GENERAL,
            top_k=3
        )
        
        response = await integration_controller.query(query_request)
        
        # 验证查询结果
        assert response.success is True
        assert "机器学习" in response.response
        assert len(response.sources) > 0
    
    async def test_cache_integration(self, integration_controller, sample_query_request):
        """测试缓存集成"""
        # 启用缓存
        integration_controller.config.enable_cache = True
        
        # 第一次查询（应该未命中缓存）
        response1 = await integration_controller.query(sample_query_request)
        assert response1.success is True
        assert response1.from_cache is False
        
        # 第二次查询（应该命中缓存）
        response2 = await integration_controller.query(sample_query_request)
        assert response2.success is True
        # 注意：在模拟模式下可能不会真正命中缓存
        
        # 验证统计
        assert integration_controller.metrics['total_queries'] == 2
    
    async def test_error_handling_integration(self, integration_controller):
        """测试错误处理集成"""
        # 创建会导致错误的查询
        invalid_request = QueryRequest(
            query="",  # 空查询
            query_type="invalid_type",
            top_k=-1  # 无效参数
        )
        
        # 执行查询
        response = await integration_controller.query(invalid_request)
        
        # 验证错误处理
        assert response is not None
        # 在模拟模式下可能仍然成功，但应该有适当的处理
        
        # 验证错误统计
        if not response.success:
            assert integration_controller.metrics['error_count'] > 0
    
    async def test_concurrent_operations(self, integration_controller, temp_dir):
        """测试并发操作"""
        # 创建多个测试文档
        test_files = []
        for i in range(3):
            test_file = temp_dir / f"concurrent_test_{i}.txt"
            test_file.write_text(f"测试文档 {i} 的内容，包含人工智能相关信息。")
            test_files.append(str(test_file))
        
        # 并发上传文档
        upload_tasks = [
            integration_controller.add_document(file_path, metadata={'batch': True})
            for file_path in test_files
        ]
        upload_results = await asyncio.gather(*upload_tasks)
        
        # 验证上传结果
        assert all(upload_results)
        
        # 并发查询
        query_requests = [
            QueryRequest(query=f"测试查询 {i}", query_type=QueryType.GENERAL)
            for i in range(5)
        ]
        
        query_tasks = [
            integration_controller.query(request)
            for request in query_requests
        ]
        query_responses = await asyncio.gather(*query_tasks)
        
        # 验证查询结果
        assert len(query_responses) == 5
        assert all(response.success for response in query_responses)
        
        # 验证统计
        assert integration_controller.metrics['total_queries'] == 5
    
    async def test_health_check_integration(self, integration_controller):
        """测试健康检查集成"""
        health_status = await integration_controller.health_check()
        
        assert isinstance(health_status, dict)
        assert 'status' in health_status
        assert 'components' in health_status
        assert 'timestamp' in health_status
        
        # 在模拟模式下，大部分组件应该是健康的
        assert health_status['status'] in ['healthy', 'degraded']
    
    async def test_metrics_integration(self, integration_controller, sample_query_request):
        """测试指标集成"""
        # 执行一些操作来生成指标
        await integration_controller.query(sample_query_request)
        await integration_controller.query(sample_query_request)
        
        # 获取指标
        metrics = await integration_controller.get_metrics()
        
        assert isinstance(metrics, dict)
        assert 'total_queries' in metrics
        assert 'cache_hit_rate' in metrics
        assert 'error_rate' in metrics
        assert 'knowledge_base_size' in metrics
        assert 'cache_size' in metrics
        
        # 验证指标值
        assert metrics['total_queries'] == 2
        assert 0 <= metrics['cache_hit_rate'] <= 1
        assert 0 <= metrics['error_rate'] <= 1
    
    async def test_pipeline_integration(self, test_config):
        """测试流水线集成"""
        pipeline = RAGPipeline(test_config)
        
        # 创建测试请求
        request = QueryRequest(
            query="人工智能的应用领域有哪些？",
            query_type=QueryType.GENERAL,
            top_k=5
        )
        
        # 执行流水线处理
        response = await pipeline.process(request)
        
        # 验证响应
        assert isinstance(response, RAGResponse)
        assert len(response.response) > 0
        assert response.confidence > 0
        assert isinstance(response.sources, list)
        assert isinstance(response.metadata, dict)
    
    async def test_knowledge_manager_integration(self, temp_dir):
        """测试知识库管理器集成"""
        # 使用模拟模式创建知识库管理器
        with patch('core.storage.database.ASYNCPG_AVAILABLE', False), \
             patch('core.storage.vector_store.CHROMADB_AVAILABLE', False):
            
            km = KnowledgeManager()
            await km.initialize()
            
            try:
                # 创建测试文档
                test_file = temp_dir / "km_integration_test.txt"
                test_file.write_text("知识库管理器集成测试内容")
                
                # 添加文档
                success = await km.add_document(str(test_file))
                assert success is True
                
                # 搜索文档
                results = await km.search_documents("集成测试", top_k=3)
                assert isinstance(results, list)
                
                # 获取统计
                stats = await km.get_statistics()
                assert isinstance(stats, dict)
                
                # 健康检查
                health = await km.health_check()
                assert health is True
                
            finally:
                await km.shutdown()
    
    @pytest.mark.slow
    async def test_performance_integration(self, integration_controller):
        """测试性能集成（标记为慢速测试）"""
        import time
        
        # 创建多个查询请求
        requests = [
            QueryRequest(query=f"性能测试查询 {i}", query_type=QueryType.GENERAL)
            for i in range(10)
        ]
        
        # 测量处理时间
        start_time = time.time()
        
        tasks = [integration_controller.query(request) for request in requests]
        responses = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证结果
        assert len(responses) == 10
        assert all(response.success for response in responses)
        
        # 验证性能（这些阈值可能需要根据实际情况调整）
        avg_time_per_query = total_time / 10
        assert avg_time_per_query < 5.0  # 每个查询平均不超过5秒
        
        # 验证统计
        metrics = await integration_controller.get_metrics()
        assert metrics['total_queries'] == 10
        assert metrics['avg_latency'] > 0
    
    async def test_configuration_integration(self):
        """测试配置集成"""
        # 测试不同配置
        configs = [
            RAGConfig(enable_cache=True, cache_ttl=300),
            RAGConfig(enable_cache=False, max_context_length=1000),
            RAGConfig(debug_mode=True, enable_monitoring=False)
        ]
        
        for config in configs:
            with patch('core.storage.database.ASYNCPG_AVAILABLE', False), \
                 patch('core.storage.vector_store.CHROMADB_AVAILABLE', False), \
                 patch('core.storage.cache_store.REDIS_AVAILABLE', False):
                
                controller = RAGController(config)
                await controller.initialize()
                
                try:
                    # 验证配置应用
                    assert controller.config.enable_cache == config.enable_cache
                    assert controller.config.cache_ttl == config.cache_ttl
                    assert controller.config.max_context_length == config.max_context_length
                    
                    # 执行基本操作
                    request = QueryRequest(query="配置测试", query_type=QueryType.GENERAL)
                    response = await controller.query(request)
                    assert response.success is True
                    
                finally:
                    await controller.shutdown()
