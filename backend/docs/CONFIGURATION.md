# ⚙️ RAG Backend 配置说明

## 📋 配置概览

RAG Backend系统支持多种配置方式，包括环境变量、配置文件和运行时参数。本文档详细说明所有可用的配置选项。

## 🔧 配置方式

### 1. 环境变量配置

推荐使用环境变量进行配置，支持 `.env` 文件：

```bash
# .env 文件示例
DEBUG=false
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# 数据库配置
POSTGRES_URL=postgresql://user:password@localhost:5432/rag_db
REDIS_URL=redis://localhost:6379/0

# AI模型配置
OPENAI_API_KEY=your-openai-key
QWEN_API_KEY=your-qwen-key

# 安全配置
JWT_SECRET=your-jwt-secret
```

### 2. 配置文件

通过 `config/settings.py` 进行配置：

```python
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 基础配置
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    log_level: str = "INFO"
    
    # 数据库配置
    postgres_url: Optional[str] = None
    redis_url: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

## 🏗️ 配置分类

### 基础服务配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `DEBUG` | bool | `false` | 调试模式开关 |
| `HOST` | str | `"0.0.0.0"` | 服务监听地址 |
| `PORT` | int | `8000` | 服务监听端口 |
| `LOG_LEVEL` | str | `"INFO"` | 日志级别 |
| `WORKERS` | int | `1` | 工作进程数 |

#### 示例配置

```bash
# 开发环境
DEBUG=true
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=DEBUG

# 生产环境
DEBUG=false
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
WORKERS=4
```

### 数据库配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `POSTGRES_URL` | str | `None` | PostgreSQL连接URL |
| `REDIS_URL` | str | `None` | Redis连接URL |
| `DATABASE_POOL_SIZE` | int | `10` | 数据库连接池大小 |
| `DATABASE_MAX_OVERFLOW` | int | `20` | 连接池最大溢出 |

#### PostgreSQL配置

```bash
# 基础配置
POSTGRES_URL=postgresql://username:password@host:port/database

# 完整配置示例
POSTGRES_URL=postgresql://rag_user:secure_password@localhost:5432/rag_db?sslmode=require

# 连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600
```

#### Redis配置

```bash
# 基础配置
REDIS_URL=redis://host:port/db

# 完整配置示例
REDIS_URL=redis://:password@localhost:6379/0

# 集群配置
REDIS_CLUSTER_NODES=redis://node1:6379,redis://node2:6379,redis://node3:6379

# 连接池配置
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=5
```

### AI模型配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | str | `None` | OpenAI API密钥 |
| `OPENAI_BASE_URL` | str | `"https://api.openai.com/v1"` | OpenAI API基础URL |
| `QWEN_API_KEY` | str | `None` | 千问API密钥 |
| `QWEN_BASE_URL` | str | `"https://dashscope.aliyuncs.com/api/v1"` | 千问API基础URL |
| `DEFAULT_MODEL` | str | `"qwen-turbo"` | 默认使用的模型 |
| `DEFAULT_EMBEDDING_MODEL` | str | `"text-embedding-v1"` | 默认嵌入模型 |

#### OpenAI配置

```bash
# 官方API
OPENAI_API_KEY=sk-your-openai-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 自定义API端点
OPENAI_API_KEY=your-key
OPENAI_BASE_URL=https://your-custom-endpoint.com/v1

# 模型配置
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7
```

#### 千问配置

```bash
# 千问API
QWEN_API_KEY=your-qwen-key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 模型配置
QWEN_DEFAULT_MODEL=qwen-turbo
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.7
```

### 缓存配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `ENABLE_CACHE` | bool | `true` | 启用缓存 |
| `CACHE_TTL` | int | `3600` | 缓存过期时间(秒) |
| `CACHE_MAX_SIZE` | int | `10000` | 内存缓存最大条目数 |
| `QUERY_CACHE_TTL` | int | `1800` | 查询缓存过期时间 |
| `SESSION_CACHE_TTL` | int | `7200` | 会话缓存过期时间 |

#### 缓存策略配置

```bash
# 基础缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600
CACHE_MAX_SIZE=10000

# 分层缓存配置
QUERY_CACHE_TTL=1800      # 30分钟
SESSION_CACHE_TTL=7200    # 2小时
DOCUMENT_CACHE_TTL=86400  # 24小时

# 缓存清理配置
CACHE_CLEANUP_INTERVAL=300  # 5分钟清理一次
CACHE_MAX_MEMORY_MB=512     # 最大内存使用512MB
```

### 检索配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `DEFAULT_TOP_K` | int | `5` | 默认检索文档数量 |
| `MAX_TOP_K` | int | `20` | 最大检索文档数量 |
| `SIMILARITY_THRESHOLD` | float | `0.7` | 相似度阈值 |
| `MAX_CONTEXT_LENGTH` | int | `2000` | 最大上下文长度 |
| `CHUNK_SIZE` | int | `1000` | 文档分块大小 |
| `CHUNK_OVERLAP` | int | `200` | 分块重叠大小 |

#### 检索策略配置

```bash
# 基础检索配置
DEFAULT_TOP_K=5
MAX_TOP_K=20
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_LENGTH=2000

# 文档处理配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_DOCUMENT_SIZE_MB=50

# 检索策略配置
RETRIEVAL_STRATEGY=hybrid  # vector, keyword, hybrid
RERANK_ENABLED=true
RERANK_TOP_K=10
```

### 安全配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `JWT_SECRET` | str | `None` | JWT密钥 |
| `JWT_ALGORITHM` | str | `"HS256"` | JWT算法 |
| `JWT_EXPIRE_MINUTES` | int | `1440` | JWT过期时间(分钟) |
| `CORS_ORIGINS` | str | `"*"` | CORS允许的源 |
| `MAX_REQUEST_SIZE_MB` | int | `100` | 最大请求大小 |

#### 安全配置示例

```bash
# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# CORS配置
CORS_ORIGINS=http://localhost:3000,https://your-frontend.com
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE
CORS_ALLOW_HEADERS=*

# 请求限制
MAX_REQUEST_SIZE_MB=100
RATE_LIMIT_PER_MINUTE=60
```

### 监控配置

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `ENABLE_MONITORING` | bool | `true` | 启用监控 |
| `METRICS_ENABLED` | bool | `true` | 启用指标收集 |
| `HEALTH_CHECK_INTERVAL` | int | `30` | 健康检查间隔(秒) |
| `LOG_REQUESTS` | bool | `true` | 记录请求日志 |

#### 监控配置示例

```bash
# 监控开关
ENABLE_MONITORING=true
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# 日志配置
LOG_REQUESTS=true
LOG_RESPONSES=false
LOG_LEVEL=INFO
LOG_FORMAT=json

# 性能监控
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2.0
MEMORY_MONITORING=true
```

## 🌍 环境特定配置

### 开发环境 (.env.dev)

```bash
DEBUG=true
LOG_LEVEL=DEBUG
HOST=127.0.0.1
PORT=8000

# 使用本地服务
POSTGRES_URL=postgresql://dev_user:dev_pass@localhost:5432/rag_dev
REDIS_URL=redis://localhost:6379/1

# 开发用API密钥
OPENAI_API_KEY=sk-dev-key
QWEN_API_KEY=dev-qwen-key

# 宽松的安全配置
JWT_SECRET=dev-secret-key
CORS_ORIGINS=*
```

### 测试环境 (.env.test)

```bash
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# 测试数据库
POSTGRES_URL=postgresql://test_user:test_pass@localhost:5432/rag_test
REDIS_URL=redis://localhost:6379/2

# 测试用配置
ENABLE_CACHE=false
CACHE_TTL=60

# 测试API密钥
OPENAI_API_KEY=test-key
QWEN_API_KEY=test-qwen-key
```

### 生产环境 (.env.prod)

```bash
DEBUG=false
LOG_LEVEL=WARNING
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 生产数据库
POSTGRES_URL=***************************************************/rag_prod
REDIS_URL=redis://:redis_password@redis-host:6379/0

# 生产API密钥
OPENAI_API_KEY=${OPENAI_API_KEY}
QWEN_API_KEY=${QWEN_API_KEY}

# 严格的安全配置
JWT_SECRET=${JWT_SECRET}
CORS_ORIGINS=https://your-domain.com
MAX_REQUEST_SIZE_MB=50
RATE_LIMIT_PER_MINUTE=30

# 性能优化
ENABLE_CACHE=true
CACHE_TTL=7200
DATABASE_POOL_SIZE=20
```

## 🔍 配置验证

### 配置检查脚本

```python
# scripts/check_config.py
import os
from config.settings import get_settings

def check_config():
    """检查配置是否正确"""
    settings = get_settings()
    
    issues = []
    
    # 检查必需配置
    if not settings.jwt_secret:
        issues.append("JWT_SECRET 未设置")
    
    if settings.debug and not settings.openai_api_key:
        issues.append("开发模式下建议设置 OPENAI_API_KEY")
    
    # 检查数据库连接
    if settings.postgres_url:
        try:
            # 测试数据库连接
            pass
        except Exception as e:
            issues.append(f"数据库连接失败: {e}")
    
    if issues:
        print("❌ 配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 配置检查通过")
        return True

if __name__ == "__main__":
    check_config()
```

### 运行配置检查

```bash
python scripts/check_config.py
```

## 📝 配置最佳实践

### 1. 安全性

- 🔐 **敏感信息**: 使用环境变量存储API密钥等敏感信息
- 🔑 **JWT密钥**: 生产环境使用强随机密钥
- 🌐 **CORS**: 生产环境限制CORS源
- 📝 **日志**: 避免在日志中记录敏感信息

### 2. 性能优化

- 🚀 **缓存**: 根据使用场景调整缓存TTL
- 🔄 **连接池**: 合理设置数据库连接池大小
- 📊 **监控**: 启用性能监控和指标收集
- 🧹 **清理**: 定期清理过期缓存和日志

### 3. 可维护性

- 📁 **分层配置**: 使用不同环境的配置文件
- 📋 **文档**: 为每个配置项添加清晰的说明
- ✅ **验证**: 实现配置验证和检查
- 🔄 **版本控制**: 配置模板纳入版本控制

---

**⚙️ 配置完成后，请运行配置检查脚本验证设置是否正确！**
