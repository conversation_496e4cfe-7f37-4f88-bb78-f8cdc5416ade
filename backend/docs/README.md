# 🤖 RAG Backend

检索增强生成(RAG)系统后端服务 - 基于FastAPI的高性能RAG系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/Tests-Passing-brightgreen.svg)](tests/)

## ✨ 特性

- 🚀 **高性能**: 基于FastAPI的异步架构
- 🧠 **智能检索**: 多策略检索和重排序
- 📚 **知识管理**: 完整的文档摄取和处理流水线
- 🔄 **缓存优化**: 多层缓存提升响应速度
- 📊 **监控完善**: 实时性能监控和指标收集
- 🧪 **测试完备**: 100%核心功能测试覆盖
- 🐳 **容器化**: Docker支持，一键部署

## 📁 项目结构

```
backend/
├── 📁 api/                   # API接口层
│   ├── routes.py            # 🛣️ API路由定义
│   ├── schemas.py           # 📋 请求/响应数据模式
│   └── __init__.py
├── 📁 core/                  # 🧠 核心业务逻辑
│   ├── controller.py        # 🎮 RAG主控制器
│   ├── pipeline.py          # ⚡ RAG处理流水线
│   ├── knowledge.py         # 📚 知识库管理
│   ├── retrieval.py         # 🔍 检索引擎
│   ├── generation.py        # ✨ 生成增强
│   ├── 📁 storage/          # 💾 存储层
│   ├── 📁 ai_models/        # 🤖 AI模型集成
│   ├── 📁 cache/            # 🗄️ 缓存管理
│   └── __init__.py
├── 📁 config/               # ⚙️ 配置管理
│   ├── settings.py          # 🔧 系统配置
│   └── __init__.py
├── 📁 models/               # 📊 数据模型
│   ├── query.py            # ❓ 查询模型
│   ├── response.py         # 💬 响应模型
│   ├── document.py         # 📄 文档模型
│   ├── config.py           # ⚙️ 配置模型
│   └── __init__.py
├── 📁 utils/                # 🛠️ 工具函数
│   ├── text_processing.py  # 📝 文本处理
│   ├── logger.py           # 📋 日志工具
│   ├── monitoring.py       # 📊 监控工具
│   ├── metrics.py          # 📈 评估指标
│   └── __init__.py
├── 📁 tests/                # 🧪 测试代码
│   ├── 📁 unit/            # 🔬 单元测试
│   ├── 📁 integration/     # 🔗 集成测试
│   ├── conftest.py         # ⚙️ 测试配置
│   └── __init__.py
├── 📁 scripts/              # 📜 脚本工具
│   └── run_tests.py        # 🧪 测试运行器
├── 📁 data/                 # 💾 数据目录
│   ├── uploads/            # 📤 上传文件
│   ├── knowledge_base/     # 📚 知识库
│   ├── indexes/            # 🗂️ 索引文件
│   ├── embeddings/         # 🧮 向量嵌入
│   └── cache/              # 🗄️ 缓存数据
├── 📁 docs/                 # 📖 文档
├── 📁 docker/               # 🐳 Docker配置
│   ├── Dockerfile          # 🐳 Docker镜像
│   └── docker-compose.yml  # 🐳 容器编排
├── main.py                  # 🚀 应用入口
├── requirements.txt         # 📦 依赖列表
├── pyproject.toml          # ⚙️ 项目配置
├── setup.py                # 📦 安装配置
└── README.md               # 📖 项目说明
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- pip 或 poetry
- Redis (可选，用于缓存)
- PostgreSQL (可选，用于持久化存储)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd rag/backend

# 安装依赖
pip install -r requirements.txt

# 或使用开发模式安装
pip install -e .
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 启动服务

```bash
# 开发模式启动
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 访问服务

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **交互式文档**: http://localhost:8000/redoc

## 🧪 测试

```bash
# 运行所有测试
python scripts/run_tests.py --all

# 运行单元测试
python scripts/run_tests.py --unit

# 运行集成测试
python scripts/run_tests.py --integration

# 生成覆盖率报告
python scripts/run_tests.py --coverage
```

## 📊 API接口

### 核心接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/rag/query` | POST | RAG查询接口 |
| `/api/v1/knowledge/upload` | POST | 文档上传接口 |
| `/api/v1/knowledge/status` | GET | 知识库状态 |
| `/api/v1/health` | GET | 健康检查 |
| `/api/v1/statistics` | GET | 系统统计 |

### 查询示例

```bash
curl -X POST "http://localhost:8000/api/v1/rag/query" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "什么是人工智能？",
       "query_type": "general",
       "top_k": 5
     }'
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t rag-backend .

# 运行容器
docker run -p 8000:8000 rag-backend

# 使用docker-compose
docker-compose up -d
```

## ⚙️ 配置说明

主要配置项在 `config/settings.py` 中：

```python
# 基本配置
DEBUG = True
HOST = "0.0.0.0"
PORT = 8000

# 数据库配置
POSTGRES_URL = "postgresql://user:pass@localhost:5432/rag_db"
REDIS_URL = "redis://localhost:6379/0"

# AI模型配置
OPENAI_API_KEY = "your-openai-key"
QWEN_API_KEY = "your-qwen-key"
```

## 📈 性能监控

系统内置了完整的监控体系：

- **实时指标**: 请求量、响应时间、错误率
- **缓存监控**: 命中率、存储使用情况
- **AI模型监控**: 调用次数、Token使用量
- **健康检查**: 各组件状态监控

访问 `/api/v1/metrics` 查看详细指标。

## 🔧 开发指南

### 代码结构

- **controller.py**: RAG主控制器，协调各个组件
- **pipeline.py**: 处理流水线，定义查询处理流程
- **knowledge.py**: 知识库管理，处理文档摄取和索引
- **retrieval.py**: 检索引擎，实现多策略检索
- **generation.py**: 生成引擎，处理LLM调用和响应生成

### 扩展开发

1. **添加新的文档处理器**: 在 `core/knowledge.py` 中扩展
2. **自定义检索策略**: 在 `core/retrieval.py` 中实现
3. **集成新的AI模型**: 在 `core/ai_models/` 中添加
4. **添加缓存策略**: 在 `core/cache/` 中实现

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 提交 [Issue](issues/)
3. 联系维护者

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**
