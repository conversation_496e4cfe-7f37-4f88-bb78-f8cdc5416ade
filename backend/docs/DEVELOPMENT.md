# 🛠️ RAG Backend 开发指南

## 🎯 开发环境设置

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd rag/backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac

# 安装开发依赖
pip install -e ".[dev]"

# 安装pre-commit钩子
pre-commit install
```

### 2. IDE配置

#### VS Code配置

```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    }
}
```

#### PyCharm配置

1. 设置Python解释器为虚拟环境
2. 配置代码格式化工具为Black
3. 启用pytest作为测试运行器
4. 配置Flake8作为代码检查工具

## 🏗️ 项目架构

### 核心组件

```
core/
├── controller.py      # 🎮 主控制器 - 协调各组件
├── pipeline.py        # ⚡ 处理流水线 - 定义查询流程
├── knowledge.py       # 📚 知识管理 - 文档处理和索引
├── retrieval.py       # 🔍 检索引擎 - 多策略检索
├── generation.py      # ✨ 生成引擎 - LLM调用和响应
├── storage/           # 💾 存储层
│   ├── database.py    # 🗄️ 数据库管理
│   ├── vector_store.py # 🧮 向量存储
│   └── cache_store.py # 🗄️ 缓存管理
├── ai_models/         # 🤖 AI模型集成
│   ├── openai_client.py
│   ├── qwen_client.py
│   └── model_manager.py
└── cache/             # 🗄️ 缓存策略
    ├── query_cache.py
    └── session_cache.py
```

### 数据流

```mermaid
graph TD
    A[用户查询] --> B[Controller]
    B --> C[Pipeline]
    C --> D[Knowledge Manager]
    D --> E[Retrieval Engine]
    E --> F[Vector Store]
    E --> G[Database]
    C --> H[Generation Engine]
    H --> I[AI Models]
    C --> J[Cache]
    C --> K[响应]
```

## 📝 编码规范

### 1. 代码风格

```python
# 使用Black格式化
black --line-length 88 .

# 使用isort排序导入
isort .

# 使用flake8检查
flake8 --max-line-length 88 --extend-ignore E203,W503 .
```

### 2. 命名规范

```python
# 类名：PascalCase
class RAGController:
    pass

# 函数和变量：snake_case
def process_query(query_text: str) -> str:
    user_input = query_text.strip()
    return user_input

# 常量：UPPER_SNAKE_CASE
MAX_QUERY_LENGTH = 1000
DEFAULT_TOP_K = 5

# 私有方法：前缀下划线
def _internal_method(self):
    pass
```

### 3. 类型注解

```python
from typing import List, Dict, Optional, Union, Any
from pydantic import BaseModel

# 函数类型注解
def search_documents(
    query: str,
    top_k: int = 5,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """搜索文档"""
    pass

# 类属性类型注解
class QueryRequest(BaseModel):
    query: str
    query_type: str = "general"
    top_k: int = 5
    filters: Optional[Dict[str, Any]] = None
```

### 4. 文档字符串

```python
def process_document(
    file_path: str,
    chunk_size: int = 1000,
    overlap: int = 200
) -> List[str]:
    """
    处理文档并分割成块
    
    Args:
        file_path: 文档文件路径
        chunk_size: 文本块大小，默认1000字符
        overlap: 块之间的重叠字符数，默认200
    
    Returns:
        分割后的文本块列表
    
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 参数无效
    
    Example:
        >>> chunks = process_document("doc.txt", chunk_size=500)
        >>> len(chunks)
        10
    """
    pass
```

## 🧪 测试开发

### 1. 测试结构

```
tests/
├── unit/              # 单元测试
│   ├── test_controller.py
│   ├── test_knowledge_manager.py
│   └── test_retrieval.py
├── integration/       # 集成测试
│   ├── test_api.py
│   └── test_rag_integration.py
├── conftest.py        # 测试配置和夹具
└── test_config.py     # 测试专用配置
```

### 2. 编写测试

```python
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock

class TestRAGController:
    """RAG控制器测试"""
    
    @pytest_asyncio.fixture
    async def controller(self, test_config):
        """控制器夹具"""
        controller = RAGController(test_config)
        await controller.initialize()
        yield controller
        await controller.shutdown()
    
    @pytest.mark.asyncio
    async def test_query_success(self, controller):
        """测试查询成功"""
        # Arrange
        query = "什么是人工智能？"
        
        # Act
        result = await controller.query(query)
        
        # Assert
        assert result.success is True
        assert len(result.response) > 0
        assert result.confidence > 0
    
    @pytest.mark.parametrize("query_type", [
        "general", "risk_assessment", "compliance_check"
    ])
    async def test_different_query_types(self, controller, query_type):
        """测试不同查询类型"""
        query = "测试查询"
        result = await controller.query(query, query_type=query_type)
        assert result.success is True
```

### 3. 运行测试

```bash
# 运行所有测试
python scripts/run_tests.py --all

# 运行特定测试
pytest tests/unit/test_controller.py -v

# 运行带覆盖率的测试
pytest --cov=core --cov=models --cov=utils --cov-report=html

# 运行性能测试
pytest tests/performance/ -v
```

## 🔧 调试技巧

### 1. 日志调试

```python
import logging
from utils.logger import get_logger

logger = get_logger(__name__)

def debug_function():
    logger.debug("调试信息")
    logger.info("一般信息")
    logger.warning("警告信息")
    logger.error("错误信息")
    
    # 使用结构化日志
    logger.info("查询处理", extra={
        "query_id": "123",
        "processing_time": 1.5,
        "result_count": 5
    })
```

### 2. 性能分析

```python
import time
import cProfile
from utils.monitoring import Timer, metrics

# 使用计时器
with Timer(metrics, "query_processing"):
    result = process_query(query)

# 使用性能分析器
def profile_function():
    pr = cProfile.Profile()
    pr.enable()
    
    # 执行代码
    result = expensive_operation()
    
    pr.disable()
    pr.dump_stats('profile_results.prof')
```

### 3. 内存调试

```python
import tracemalloc
import psutil
import os

# 内存跟踪
tracemalloc.start()

# 执行代码
result = memory_intensive_operation()

# 获取内存使用情况
current, peak = tracemalloc.get_traced_memory()
print(f"当前内存: {current / 1024 / 1024:.1f} MB")
print(f"峰值内存: {peak / 1024 / 1024:.1f} MB")

# 系统内存使用
process = psutil.Process(os.getpid())
memory_info = process.memory_info()
print(f"RSS内存: {memory_info.rss / 1024 / 1024:.1f} MB")
```

## 🚀 新功能开发

### 1. 添加新的检索策略

```python
# core/retrieval.py
class CustomRetrievalStrategy(RetrievalStrategy):
    """自定义检索策略"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def retrieve(
        self, 
        query: str, 
        top_k: int = 5
    ) -> List[RetrievalResult]:
        """实现检索逻辑"""
        # 1. 查询预处理
        processed_query = self._preprocess_query(query)
        
        # 2. 执行检索
        results = await self._search(processed_query, top_k)
        
        # 3. 结果后处理
        return self._postprocess_results(results)
    
    def _preprocess_query(self, query: str) -> str:
        """查询预处理"""
        pass
    
    async def _search(self, query: str, top_k: int) -> List[Dict]:
        """执行搜索"""
        pass
    
    def _postprocess_results(self, results: List[Dict]) -> List[RetrievalResult]:
        """结果后处理"""
        pass

# 注册策略
RETRIEVAL_STRATEGIES = {
    "custom": CustomRetrievalStrategy,
    # ... 其他策略
}
```

### 2. 集成新的AI模型

```python
# core/ai_models/custom_model.py
class CustomModelClient(BaseModelClient):
    """自定义模型客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get("api_key")
        self.base_url = config.get("base_url")
        self.model_name = config.get("model_name")
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> ModelResponse:
        """聊天完成"""
        try:
            # 调用模型API
            response = await self._call_api(messages, **kwargs)
            
            return ModelResponse(
                content=response["content"],
                model=self.model_name,
                usage=response.get("usage", {}),
                finish_reason=response.get("finish_reason"),
                latency=response.get("latency", 0),
                error=None
            )
        except Exception as e:
            return ModelResponse(
                content="",
                model=self.model_name,
                error=str(e)
            )
    
    async def create_embeddings(
        self,
        texts: List[str],
        **kwargs
    ) -> EmbeddingResponse:
        """创建嵌入"""
        # 实现嵌入逻辑
        pass
```

### 3. 添加新的缓存策略

```python
# core/cache/custom_cache.py
class CustomCacheManager(BaseCacheManager):
    """自定义缓存管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache_store = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        return self.cache_store.get(key)
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存"""
        self.cache_store[key] = {
            "value": value,
            "expires_at": time.time() + (ttl or 3600)
        }
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        return self.cache_store.pop(key, None) is not None
```

## 📊 性能优化

### 1. 异步优化

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 并发处理
async def process_documents_concurrently(documents: List[str]):
    tasks = [process_document(doc) for doc in documents]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results

# 线程池处理CPU密集型任务
executor = ThreadPoolExecutor(max_workers=4)

async def cpu_intensive_task(data):
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, heavy_computation, data)
    return result
```

### 2. 内存优化

```python
# 使用生成器减少内存使用
def process_large_file(file_path: str):
    with open(file_path, 'r') as f:
        for line in f:
            yield process_line(line)

# 批处理
async def batch_process(items: List[Any], batch_size: int = 100):
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        await process_batch(batch)
```

### 3. 缓存优化

```python
from functools import lru_cache
import asyncio

# 内存缓存
@lru_cache(maxsize=1000)
def expensive_computation(input_data: str) -> str:
    # 昂贵的计算
    return result

# 异步缓存
class AsyncLRUCache:
    def __init__(self, maxsize: int = 128):
        self.cache = {}
        self.maxsize = maxsize
    
    async def get_or_compute(self, key: str, compute_func):
        if key in self.cache:
            return self.cache[key]
        
        result = await compute_func()
        
        if len(self.cache) >= self.maxsize:
            # 移除最旧的项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[key] = result
        return result
```

## 🔍 代码审查清单

### 提交前检查

- [ ] 代码格式化（Black）
- [ ] 导入排序（isort）
- [ ] 代码检查（flake8）
- [ ] 类型检查（mypy）
- [ ] 测试通过
- [ ] 文档更新
- [ ] 变更日志更新

### 代码审查要点

1. **功能正确性**
   - 逻辑是否正确
   - 边界条件处理
   - 错误处理是否完善

2. **性能考虑**
   - 是否有性能瓶颈
   - 内存使用是否合理
   - 异步操作是否正确

3. **代码质量**
   - 代码可读性
   - 函数复杂度
   - 重复代码

4. **安全性**
   - 输入验证
   - 敏感信息处理
   - 权限检查

## 📚 学习资源

### 推荐阅读

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Pydantic文档](https://pydantic-docs.helpmanual.io/)
- [pytest文档](https://docs.pytest.org/)
- [Python异步编程指南](https://docs.python.org/3/library/asyncio.html)

### 相关项目

- [LangChain](https://github.com/hwchase17/langchain)
- [LlamaIndex](https://github.com/jerryjliu/llama_index)
- [Haystack](https://github.com/deepset-ai/haystack)

---

**🎯 开发愉快！如有问题，请查看文档或提交Issue。**
