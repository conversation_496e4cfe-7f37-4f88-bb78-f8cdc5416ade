# 🚀 RAG Backend 部署指南

## 📋 部署概览

本文档提供RAG Backend系统的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 🛠️ 环境要求

### 基础要求

- **Python**: 3.8+
- **内存**: 最少4GB，推荐8GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的互联网连接（用于AI模型API调用）

### 依赖服务（可选）

- **PostgreSQL**: 11+ (持久化存储)
- **Redis**: 6+ (缓存)
- **Docker**: 20+ (容器化部署)
- **Nginx**: 1.18+ (反向代理)

## 🏠 本地开发环境

### 1. 克隆项目

```bash
git clone <repository-url>
cd rag/backend
```

### 2. 创建虚拟环境

```bash
# 使用venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 使用conda
conda create -n rag-backend python=3.9
conda activate rag-backend
```

### 3. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"
```

### 4. 配置环境变量

```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

`.env` 文件示例：

```bash
# 基础配置
DEBUG=true
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# 数据库配置（可选）
POSTGRES_URL=postgresql://user:password@localhost:5432/rag_db
REDIS_URL=redis://localhost:6379/0

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
QWEN_API_KEY=your-qwen-api-key

# 安全配置
JWT_SECRET=your-jwt-secret-key
```

### 5. 初始化数据库（可选）

```bash
# 如果使用PostgreSQL
python scripts/init_database.py
```

### 6. 启动服务

```bash
# 直接启动
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 7. 验证部署

```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 查看API文档
open http://localhost:8000/docs
```

## 🧪 测试环境部署

### 使用Docker Compose

```bash
# 启动所有服务
docker-compose -f docker/docker-compose.test.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f rag-backend
```

### 测试环境配置

```yaml
# docker/docker-compose.test.yml
version: '3.8'

services:
  rag-backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - POSTGRES_URL=********************************************/rag_test
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./data:/app/data

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: rag_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## 🏭 生产环境部署

### 方案1: Docker部署

#### 1. 构建生产镜像

```bash
# 构建镜像
docker build -t rag-backend:latest -f docker/Dockerfile.prod .

# 推送到镜像仓库
docker tag rag-backend:latest your-registry/rag-backend:latest
docker push your-registry/rag-backend:latest
```

#### 2. 生产环境配置

```yaml
# docker/docker-compose.prod.yml
version: '3.8'

services:
  rag-backend:
    image: your-registry/rag-backend:latest
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - WORKERS=4
    env_file:
      - .env.prod
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - rag-backend
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: rag_prod
      POSTGRES_USER: rag_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### 3. Nginx配置

```nginx
# nginx/nginx.conf
upstream rag_backend {
    server rag-backend:8000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    client_max_body_size 100M;

    location / {
        proxy_pass http://rag_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    location /api/v1/knowledge/upload {
        proxy_pass http://rag_backend;
        proxy_request_buffering off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        client_max_body_size 500M;
    }
}
```

### 方案2: Kubernetes部署

#### 1. 创建命名空间

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: rag-system
```

#### 2. 配置ConfigMap和Secret

```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-config
  namespace: rag-system
data:
  DEBUG: "false"
  HOST: "0.0.0.0"
  PORT: "8000"
  LOG_LEVEL: "INFO"

---
apiVersion: v1
kind: Secret
metadata:
  name: rag-secrets
  namespace: rag-system
type: Opaque
stringData:
  POSTGRES_URL: "****************************************/rag_db"
  REDIS_URL: "redis://redis:6379/0"
  OPENAI_API_KEY: "your-openai-key"
  QWEN_API_KEY: "your-qwen-key"
  JWT_SECRET: "your-jwt-secret"
```

#### 3. 部署应用

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-backend
  namespace: rag-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rag-backend
  template:
    metadata:
      labels:
        app: rag-backend
    spec:
      containers:
      - name: rag-backend
        image: your-registry/rag-backend:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: rag-config
        - secretRef:
            name: rag-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: rag-backend-service
  namespace: rag-system
spec:
  selector:
    app: rag-backend
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
```

## 📊 监控和日志

### 1. 日志配置

```python
# config/logging.py
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "json": {
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/rag.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "json",
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file"],
    },
}
```

### 2. 健康检查

```bash
# 创建健康检查脚本
cat > scripts/health_check.sh << 'EOF'
#!/bin/bash

ENDPOINT="http://localhost:8000/api/v1/health"
TIMEOUT=10

response=$(curl -s -w "%{http_code}" -o /tmp/health_response --max-time $TIMEOUT "$ENDPOINT")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo "✅ Service is healthy"
    exit 0
else
    echo "❌ Service is unhealthy (HTTP $http_code)"
    cat /tmp/health_response
    exit 1
fi
EOF

chmod +x scripts/health_check.sh
```

## 🔧 性能优化

### 1. 应用优化

```python
# main.py 生产配置
if not settings.debug:
    # 启用多进程
    import multiprocessing
    workers = multiprocessing.cpu_count() * 2 + 1
    
    # 配置uvicorn
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        workers=workers,
        access_log=False,
        log_config=LOGGING_CONFIG
    )
```

### 2. 数据库优化

```sql
-- PostgreSQL优化
-- 创建索引
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_queries_created_at ON queries(created_at);

-- 配置连接池
-- postgresql.conf
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
```

### 3. Redis优化

```bash
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🔒 安全配置

### 1. 环境变量安全

```bash
# 使用强密码
JWT_SECRET=$(openssl rand -base64 32)
POSTGRES_PASSWORD=$(openssl rand -base64 16)

# 限制文件权限
chmod 600 .env.prod
```

### 2. 网络安全

```yaml
# docker-compose.prod.yml 网络配置
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

services:
  rag-backend:
    networks:
      - frontend
      - backend
  
  postgres:
    networks:
      - backend
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   docker-compose logs rag-backend
   
   # 检查端口占用
   netstat -tulpn | grep :8000
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   psql -h localhost -U rag_user -d rag_db
   
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   ```

3. **内存不足**
   ```bash
   # 监控内存使用
   docker stats
   
   # 调整内存限制
   # 在docker-compose.yml中添加
   deploy:
     resources:
       limits:
         memory: 2G
   ```

### 性能问题诊断

```bash
# 查看API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/v1/health"

# 监控系统资源
htop
iotop
```

## 📋 部署检查清单

### 部署前检查

- [ ] 环境变量配置完成
- [ ] 数据库连接测试通过
- [ ] AI模型API密钥有效
- [ ] SSL证书配置正确
- [ ] 防火墙规则设置
- [ ] 备份策略制定

### 部署后验证

- [ ] 健康检查通过
- [ ] API接口正常响应
- [ ] 文档上传功能正常
- [ ] RAG查询功能正常
- [ ] 日志记录正常
- [ ] 监控指标正常

---

**🎯 部署成功后，请访问 `/docs` 查看API文档，确保所有功能正常工作！**
