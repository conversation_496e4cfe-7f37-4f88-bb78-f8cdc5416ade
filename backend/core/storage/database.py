"""
PostgreSQL数据库管理器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from contextlib import asynccontextmanager

try:
    import asyncpg
    from asyncpg import Pool, Connection
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False

from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class DatabaseManager:
    """
    PostgreSQL数据库管理器
    
    负责：
    1. 数据库连接池管理
    2. 文档元数据存储
    3. 查询历史记录
    4. 系统配置存储
    5. 用户会话管理
    """
    
    def __init__(self):
        self.pool: Optional[Pool] = None
        self.connected = False
        self._connection_retries = 3
        self._connection_timeout = 30
        
        logger.info("数据库管理器初始化")
    
    async def connect(self) -> bool:
        """连接数据库"""
        if not ASYNCPG_AVAILABLE:
            logger.warning("asyncpg未安装，使用模拟数据库")
            self.connected = True
            return True
        
        if not hasattr(settings, 'postgres_url') or not settings.postgres_url:
            logger.warning("PostgreSQL连接字符串未配置，使用模拟数据库")
            self.connected = True
            return True
        
        try:
            logger.info("连接PostgreSQL数据库...")
            
            # 创建连接池
            self.pool = await asyncpg.create_pool(
                settings.postgres_url,
                min_size=2,
                max_size=10,
                timeout=self._connection_timeout,
                command_timeout=60
            )
            
            # 测试连接
            async with self.pool.acquire() as conn:
                await conn.execute('SELECT 1')
            
            # 初始化数据库表
            await self._init_tables()
            
            self.connected = True
            logger.info("PostgreSQL数据库连接成功")
            return True
            
        except Exception as e:
            logger.error(f"PostgreSQL数据库连接失败: {e}")
            # 在开发环境下使用模拟模式
            if settings.debug:
                logger.info("开发环境：启用数据库模拟模式")
                self.connected = True
                return True
            return False
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.pool:
            try:
                await self.pool.close()
                logger.info("数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭数据库连接失败: {e}")
        
        self.connected = False
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接"""
        if not self.connected:
            raise RuntimeError("数据库未连接")
        
        if not self.pool:
            # 模拟连接
            yield None
            return
        
        async with self.pool.acquire() as conn:
            yield conn
    
    async def _init_tables(self):
        """初始化数据库表"""
        if not self.pool:
            return
        
        try:
            async with self.pool.acquire() as conn:
                # 创建文档表
                await conn.execute('''
                    CREATE TABLE IF NOT EXISTS documents (
                        id VARCHAR(255) PRIMARY KEY,
                        filename VARCHAR(500) NOT NULL,
                        file_path VARCHAR(1000),
                        file_size BIGINT,
                        file_type VARCHAR(50),
                        content_hash VARCHAR(64),
                        metadata JSONB,
                        status VARCHAR(50) DEFAULT 'processing',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        processed_at TIMESTAMP
                    )
                ''')
                
                # 创建文档块表
                await conn.execute('''
                    CREATE TABLE IF NOT EXISTS document_chunks (
                        id VARCHAR(255) PRIMARY KEY,
                        document_id VARCHAR(255) REFERENCES documents(id) ON DELETE CASCADE,
                        chunk_index INTEGER NOT NULL,
                        content TEXT NOT NULL,
                        content_hash VARCHAR(64),
                        start_index INTEGER,
                        end_index INTEGER,
                        metadata JSONB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建查询历史表
                await conn.execute('''
                    CREATE TABLE IF NOT EXISTS query_history (
                        id VARCHAR(255) PRIMARY KEY,
                        query_text TEXT NOT NULL,
                        query_type VARCHAR(100),
                        response_text TEXT,
                        confidence FLOAT,
                        processing_time FLOAT,
                        source_count INTEGER,
                        metadata JSONB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建系统配置表
                await conn.execute('''
                    CREATE TABLE IF NOT EXISTS system_config (
                        key VARCHAR(255) PRIMARY KEY,
                        value JSONB NOT NULL,
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                await conn.execute('CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status)')
                await conn.execute('CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)')
                await conn.execute('CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON document_chunks(document_id)')
                await conn.execute('CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at)')
                
                logger.info("数据库表初始化完成")
                
        except Exception as e:
            logger.error(f"数据库表初始化失败: {e}")
            raise
    
    async def save_document(self, document_id: str, filename: str, 
                          file_path: str, metadata: Dict[str, Any]) -> bool:
        """保存文档信息"""
        try:
            if not self.pool:
                # 模拟保存
                logger.debug(f"模拟保存文档: {document_id}")
                return True
            
            async with self.pool.acquire() as conn:
                await conn.execute('''
                    INSERT INTO documents (id, filename, file_path, file_size, file_type, metadata, status)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (id) DO UPDATE SET
                        filename = EXCLUDED.filename,
                        file_path = EXCLUDED.file_path,
                        metadata = EXCLUDED.metadata,
                        updated_at = CURRENT_TIMESTAMP
                ''', document_id, filename, file_path, 
                    metadata.get('file_size', 0),
                    metadata.get('file_extension', ''),
                    json.dumps(metadata),
                    'processing'
                )
            
            logger.debug(f"文档信息已保存: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存文档信息失败: {e}")
            return False
    
    async def save_document_chunks(self, chunks: List[Dict[str, Any]]) -> bool:
        """批量保存文档块"""
        try:
            if not self.pool or not chunks:
                return True
            
            async with self.pool.acquire() as conn:
                # 准备批量插入数据
                chunk_data = [
                    (
                        chunk['id'],
                        chunk['document_id'],
                        chunk['chunk_index'],
                        chunk['content'],
                        chunk.get('content_hash', ''),
                        chunk.get('start_index', 0),
                        chunk.get('end_index', 0),
                        json.dumps(chunk.get('metadata', {}))
                    )
                    for chunk in chunks
                ]
                
                await conn.executemany('''
                    INSERT INTO document_chunks 
                    (id, document_id, chunk_index, content, content_hash, start_index, end_index, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (id) DO UPDATE SET
                        content = EXCLUDED.content,
                        metadata = EXCLUDED.metadata
                ''', chunk_data)
            
            logger.debug(f"已保存 {len(chunks)} 个文档块")
            return True
            
        except Exception as e:
            logger.error(f"保存文档块失败: {e}")
            return False
    
    async def update_document_status(self, document_id: str, status: str) -> bool:
        """更新文档状态"""
        try:
            if not self.pool:
                return True
            
            async with self.pool.acquire() as conn:
                await conn.execute('''
                    UPDATE documents 
                    SET status = $1, updated_at = CURRENT_TIMESTAMP,
                        processed_at = CASE WHEN $1 = 'completed' THEN CURRENT_TIMESTAMP ELSE processed_at END
                    WHERE id = $2
                ''', status, document_id)
            
            logger.debug(f"文档状态已更新: {document_id} -> {status}")
            return True
            
        except Exception as e:
            logger.error(f"更新文档状态失败: {e}")
            return False
    
    async def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """获取文档信息"""
        try:
            if not self.pool:
                return None
            
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow('''
                    SELECT id, filename, file_path, file_size, file_type, 
                           metadata, status, created_at, updated_at, processed_at
                    FROM documents WHERE id = $1
                ''', document_id)
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            logger.error(f"获取文档信息失败: {e}")
            return None
    
    async def list_documents(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """列出文档"""
        try:
            if not self.pool:
                return []
            
            async with self.pool.acquire() as conn:
                rows = await conn.fetch('''
                    SELECT id, filename, file_size, file_type, status, created_at
                    FROM documents 
                    ORDER BY created_at DESC
                    LIMIT $1 OFFSET $2
                ''', limit, offset)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"列出文档失败: {e}")
            return []
    
    async def delete_document(self, document_id: str) -> bool:
        """删除文档"""
        try:
            if not self.pool:
                return True
            
            async with self.pool.acquire() as conn:
                # 删除文档（级联删除文档块）
                await conn.execute('DELETE FROM documents WHERE id = $1', document_id)
            
            logger.debug(f"文档已删除: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    async def save_query_history(self, query_id: str, query_text: str, 
                               query_type: str, response_text: str,
                               confidence: float, processing_time: float,
                               source_count: int, metadata: Dict[str, Any]) -> bool:
        """保存查询历史"""
        try:
            if not self.pool:
                return True
            
            async with self.pool.acquire() as conn:
                await conn.execute('''
                    INSERT INTO query_history 
                    (id, query_text, query_type, response_text, confidence, 
                     processing_time, source_count, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ''', query_id, query_text, query_type, response_text,
                    confidence, processing_time, source_count, json.dumps(metadata))
            
            logger.debug(f"查询历史已保存: {query_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存查询历史失败: {e}")
            return False
    
    async def get_query_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取查询历史"""
        try:
            if not self.pool:
                return []
            
            async with self.pool.acquire() as conn:
                rows = await conn.fetch('''
                    SELECT id, query_text, query_type, confidence, 
                           processing_time, source_count, created_at
                    FROM query_history 
                    ORDER BY created_at DESC
                    LIMIT $1
                ''', limit)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取查询历史失败: {e}")
            return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            if not self.pool:
                return {
                    'total_documents': 0,
                    'total_chunks': 0,
                    'total_queries': 0,
                    'documents_by_status': {},
                    'recent_activity': 0
                }
            
            async with self.pool.acquire() as conn:
                # 文档统计
                doc_count = await conn.fetchval('SELECT COUNT(*) FROM documents')
                chunk_count = await conn.fetchval('SELECT COUNT(*) FROM document_chunks')
                query_count = await conn.fetchval('SELECT COUNT(*) FROM query_history')
                
                # 按状态统计文档
                status_rows = await conn.fetch('''
                    SELECT status, COUNT(*) as count 
                    FROM documents 
                    GROUP BY status
                ''')
                status_stats = {row['status']: row['count'] for row in status_rows}
                
                # 最近24小时活动
                recent_activity = await conn.fetchval('''
                    SELECT COUNT(*) FROM query_history 
                    WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
                ''')
                
                return {
                    'total_documents': doc_count,
                    'total_chunks': chunk_count,
                    'total_queries': query_count,
                    'documents_by_status': status_stats,
                    'recent_activity': recent_activity
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.connected:
                return False
            
            if not self.pool:
                return True  # 模拟模式
            
            async with self.pool.acquire() as conn:
                await conn.execute('SELECT 1')
            
            return True
            
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False
