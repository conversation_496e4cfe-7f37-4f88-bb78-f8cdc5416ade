"""
Redis缓存管理器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

try:
    import redis.asyncio as redis
    from redis.asyncio import Redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class CacheManager:
    """
    Redis缓存管理器
    
    负责：
    1. Redis连接管理
    2. 查询结果缓存
    3. 会话数据缓存
    4. 临时数据存储
    5. 缓存失效管理
    """
    
    def __init__(self):
        self.redis_client: Optional[Redis] = None
        self.connected = False
        self.default_ttl = settings.cache_ttl if hasattr(settings, 'cache_ttl') else 3600
        self.key_prefix = "rag:"
        
        # 内存缓存（模拟模式）
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info("缓存管理器初始化")
    
    async def connect(self) -> bool:
        """连接Redis"""
        if not REDIS_AVAILABLE:
            logger.warning("redis未安装，使用内存缓存")
            self.connected = True
            return True
        
        if not hasattr(settings, 'redis_url') or not settings.redis_url:
            logger.warning("Redis连接字符串未配置，使用内存缓存")
            self.connected = True
            return True
        
        try:
            logger.info("连接Redis...")
            
            # 创建Redis连接
            self.redis_client = redis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=False,  # 我们将手动处理编码
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            
            self.connected = True
            logger.info("Redis连接成功")
            return True
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            # 在开发环境下使用内存缓存
            if settings.debug:
                logger.info("开发环境：启用内存缓存模式")
                self.connected = True
                return True
            return False
    
    async def disconnect(self):
        """断开Redis连接"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接失败: {e}")
        
        self.connected = False
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.key_prefix}{key}"
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            if not self.connected:
                return False
            
            cache_key = self._make_key(key)
            expire_time = ttl or self.default_ttl
            
            if not self.redis_client:
                # 内存缓存模式
                expire_at = datetime.utcnow() + timedelta(seconds=expire_time)
                self._memory_cache[cache_key] = {
                    'value': value,
                    'expire_at': expire_at
                }
                logger.debug(f"内存缓存设置: {key}")
                return True
            
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, str):
                serialized_value = value
            else:
                serialized_value = pickle.dumps(value)
            
            # 设置到Redis
            await self.redis_client.setex(
                cache_key, 
                expire_time, 
                serialized_value
            )
            
            logger.debug(f"缓存已设置: {key} (TTL: {expire_time}s)")
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, 错误: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            if not self.connected:
                return None
            
            cache_key = self._make_key(key)
            
            if not self.redis_client:
                # 内存缓存模式
                if cache_key in self._memory_cache:
                    cache_item = self._memory_cache[cache_key]
                    if datetime.utcnow() < cache_item['expire_at']:
                        logger.debug(f"内存缓存命中: {key}")
                        return cache_item['value']
                    else:
                        # 缓存过期，删除
                        del self._memory_cache[cache_key]
                        logger.debug(f"内存缓存过期: {key}")
                return None
            
            # 从Redis获取
            value = await self.redis_client.get(cache_key)
            if value is None:
                return None
            
            # 反序列化值
            try:
                # 尝试JSON反序列化
                return json.loads(value)
            except (json.JSONDecodeError, UnicodeDecodeError):
                try:
                    # 尝试pickle反序列化
                    return pickle.loads(value)
                except:
                    # 返回原始字符串
                    return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"获取缓存失败: {key}, 错误: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if not self.connected:
                return False
            
            cache_key = self._make_key(key)
            
            if not self.redis_client:
                # 内存缓存模式
                if cache_key in self._memory_cache:
                    del self._memory_cache[cache_key]
                    logger.debug(f"内存缓存删除: {key}")
                return True
            
            # 从Redis删除
            result = await self.redis_client.delete(cache_key)
            logger.debug(f"缓存已删除: {key}")
            return result > 0
            
        except Exception as e:
            logger.error(f"删除缓存失败: {key}, 错误: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if not self.connected:
                return False
            
            cache_key = self._make_key(key)
            
            if not self.redis_client:
                # 内存缓存模式
                if cache_key in self._memory_cache:
                    cache_item = self._memory_cache[cache_key]
                    if datetime.utcnow() < cache_item['expire_at']:
                        return True
                    else:
                        del self._memory_cache[cache_key]
                return False
            
            # 检查Redis
            result = await self.redis_client.exists(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"检查缓存存在失败: {key}, 错误: {e}")
            return False
    
    async def set_hash(self, key: str, field: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置哈希缓存"""
        try:
            if not self.connected:
                return False
            
            cache_key = self._make_key(key)
            
            if not self.redis_client:
                # 内存缓存模式
                if cache_key not in self._memory_cache:
                    expire_at = datetime.utcnow() + timedelta(seconds=ttl or self.default_ttl)
                    self._memory_cache[cache_key] = {
                        'value': {},
                        'expire_at': expire_at
                    }
                self._memory_cache[cache_key]['value'][field] = value
                return True
            
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = str(value)
            
            # 设置到Redis哈希
            await self.redis_client.hset(cache_key, field, serialized_value)
            
            # 设置过期时间
            if ttl:
                await self.redis_client.expire(cache_key, ttl)
            
            logger.debug(f"哈希缓存已设置: {key}.{field}")
            return True
            
        except Exception as e:
            logger.error(f"设置哈希缓存失败: {key}.{field}, 错误: {e}")
            return False
    
    async def get_hash(self, key: str, field: str) -> Optional[Any]:
        """获取哈希缓存"""
        try:
            if not self.connected:
                return None
            
            cache_key = self._make_key(key)
            
            if not self.redis_client:
                # 内存缓存模式
                if cache_key in self._memory_cache:
                    cache_item = self._memory_cache[cache_key]
                    if datetime.utcnow() < cache_item['expire_at']:
                        return cache_item['value'].get(field)
                    else:
                        del self._memory_cache[cache_key]
                return None
            
            # 从Redis哈希获取
            value = await self.redis_client.hget(cache_key, field)
            if value is None:
                return None
            
            # 反序列化值
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"获取哈希缓存失败: {key}.{field}, 错误: {e}")
            return None
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            if not self.connected:
                return 0
            
            full_pattern = self._make_key(pattern)
            
            if not self.redis_client:
                # 内存缓存模式
                keys_to_delete = [
                    key for key in self._memory_cache.keys() 
                    if key.startswith(full_pattern.replace('*', ''))
                ]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                logger.debug(f"内存缓存清除: {len(keys_to_delete)}个键")
                return len(keys_to_delete)
            
            # Redis模式匹配删除
            keys = await self.redis_client.keys(full_pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.debug(f"缓存清除: {deleted}个键")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error(f"清除缓存模式失败: {pattern}, 错误: {e}")
            return 0
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if not self.connected:
                return {
                    'total_keys': 0,
                    'memory_usage': 0,
                    'hit_rate': 0.0
                }
            
            if not self.redis_client:
                # 内存缓存统计
                total_keys = len(self._memory_cache)
                # 清理过期键
                current_time = datetime.utcnow()
                expired_keys = [
                    key for key, item in self._memory_cache.items()
                    if current_time >= item['expire_at']
                ]
                for key in expired_keys:
                    del self._memory_cache[key]
                
                return {
                    'total_keys': total_keys - len(expired_keys),
                    'memory_usage': 0,
                    'hit_rate': 0.0,
                    'cache_type': 'memory'
                }
            
            # Redis统计
            info = await self.redis_client.info()
            
            # 获取RAG相关的键数量
            rag_keys = await self.redis_client.keys(f"{self.key_prefix}*")
            
            return {
                'total_keys': len(rag_keys),
                'memory_usage': info.get('used_memory', 0),
                'hit_rate': info.get('keyspace_hit_rate', 0.0),
                'cache_type': 'redis',
                'redis_version': info.get('redis_version', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.connected:
                return False
            
            if not self.redis_client:
                return True  # 内存缓存模式
            
            # 测试Redis连接
            await self.redis_client.ping()
            return True
            
        except Exception as e:
            logger.error(f"缓存健康检查失败: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1, ttl: Optional[int] = None) -> int:
        """递增计数器"""
        try:
            if not self.connected:
                return 0

            cache_key = self._make_key(key)

            if not self.redis_client:
                # 内存缓存模式
                if cache_key not in self._memory_cache:
                    expire_at = datetime.utcnow() + timedelta(seconds=ttl or self.default_ttl)
                    self._memory_cache[cache_key] = {
                        'value': 0,
                        'expire_at': expire_at
                    }

                cache_item = self._memory_cache[cache_key]
                if datetime.utcnow() < cache_item['expire_at']:
                    cache_item['value'] += amount
                    return cache_item['value']
                else:
                    del self._memory_cache[cache_key]
                    return 0

            # Redis递增
            result = await self.redis_client.incrby(cache_key, amount)

            # 设置过期时间
            if ttl:
                await self.redis_client.expire(cache_key, ttl)

            return result

        except Exception as e:
            logger.error(f"递增计数器失败: {key}, 错误: {e}")
            return 0

    async def get_list(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """获取列表"""
        try:
            if not self.connected:
                return []

            cache_key = self._make_key(key)

            if not self.redis_client:
                # 内存缓存模式
                if cache_key in self._memory_cache:
                    cache_item = self._memory_cache[cache_key]
                    if datetime.utcnow() < cache_item['expire_at']:
                        value = cache_item['value']
                        if isinstance(value, list):
                            return value[start:end+1 if end != -1 else None]
                return []

            # Redis列表操作
            items = await self.redis_client.lrange(cache_key, start, end)

            # 反序列化
            result = []
            for item in items:
                try:
                    result.append(json.loads(item))
                except json.JSONDecodeError:
                    result.append(item.decode('utf-8') if isinstance(item, bytes) else item)

            return result

        except Exception as e:
            logger.error(f"获取列表失败: {key}, 错误: {e}")
            return []

    async def push_list(self, key: str, *values: Any, ttl: Optional[int] = None) -> int:
        """向列表推送值"""
        try:
            if not self.connected:
                return 0

            cache_key = self._make_key(key)

            if not self.redis_client:
                # 内存缓存模式
                if cache_key not in self._memory_cache:
                    expire_at = datetime.utcnow() + timedelta(seconds=ttl or self.default_ttl)
                    self._memory_cache[cache_key] = {
                        'value': [],
                        'expire_at': expire_at
                    }

                cache_item = self._memory_cache[cache_key]
                if datetime.utcnow() < cache_item['expire_at']:
                    cache_item['value'].extend(values)
                    return len(cache_item['value'])
                else:
                    del self._memory_cache[cache_key]
                    return 0

            # 序列化值
            serialized_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(str(value))

            # Redis列表推送
            result = await self.redis_client.rpush(cache_key, *serialized_values)

            # 设置过期时间
            if ttl:
                await self.redis_client.expire(cache_key, ttl)

            return result

        except Exception as e:
            logger.error(f"推送列表失败: {key}, 错误: {e}")
            return 0

    async def flush_all(self) -> bool:
        """清空所有缓存"""
        try:
            if not self.connected:
                return False

            if not self.redis_client:
                # 清空内存缓存
                self._memory_cache.clear()
                logger.info("内存缓存已清空")
                return True

            # 只清空RAG相关的键
            rag_keys = await self.redis_client.keys(f"{self.key_prefix}*")
            if rag_keys:
                await self.redis_client.delete(*rag_keys)
                logger.info(f"已清空 {len(rag_keys)} 个RAG缓存键")

            return True

        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
