"""
查询缓存管理器
专门处理RAG查询结果的缓存
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

from ..storage.cache_store import CacheManager
from models.query import QueryRequest
from models.response import RAGResponse
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    query_hash: str
    query_text: str
    response: RAGResponse
    created_at: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None


@dataclass
class CacheStats:
    """缓存统计"""
    total_entries: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0


class QueryCacheManager:
    """
    查询缓存管理器
    
    功能：
    1. 智能查询缓存
    2. 缓存失效策略
    3. 热点查询识别
    4. 缓存预热
    5. 统计分析
    """
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.cache_prefix = "query_cache:"
        self.stats_key = "query_cache:stats"
        self.hot_queries_key = "query_cache:hot_queries"
        
        # 缓存配置
        self.default_ttl = 3600  # 1小时
        self.hot_query_ttl = 7200  # 2小时
        self.max_cache_size = 10000
        self.similarity_threshold = 0.9
        
        # 统计信息
        self.stats = CacheStats()
        
        logger.info("查询缓存管理器初始化完成")
    
    async def get_cached_response(self, request: QueryRequest) -> Optional[RAGResponse]:
        """获取缓存的响应"""
        try:
            # 生成查询哈希
            query_hash = self._generate_query_hash(request)
            cache_key = f"{self.cache_prefix}{query_hash}"
            
            # 尝试获取缓存
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                # 更新访问统计
                await self._update_access_stats(query_hash)
                self.stats.hit_count += 1
                
                # 反序列化响应
                response = self._deserialize_response(cached_data)
                logger.debug(f"缓存命中: {query_hash}")
                return response
            else:
                self.stats.miss_count += 1
                logger.debug(f"缓存未命中: {query_hash}")
                return None
                
        except Exception as e:
            logger.error(f"获取缓存响应失败: {e}")
            return None
    
    async def cache_response(self, request: QueryRequest, response: RAGResponse) -> bool:
        """缓存响应"""
        try:
            # 生成查询哈希
            query_hash = self._generate_query_hash(request)
            cache_key = f"{self.cache_prefix}{query_hash}"
            
            # 创建缓存条目
            cache_entry = CacheEntry(
                query_hash=query_hash,
                query_text=request.query,
                response=response,
                created_at=datetime.utcnow()
            )
            
            # 序列化缓存条目
            serialized_entry = self._serialize_cache_entry(cache_entry)
            
            # 确定TTL
            ttl = await self._determine_ttl(request.query)
            
            # 存储到缓存
            success = await self.cache_manager.set(cache_key, serialized_entry, ttl)
            
            if success:
                # 更新统计
                await self._update_cache_stats()
                
                # 检查是否为热点查询
                await self._check_hot_query(request.query)
                
                logger.debug(f"响应已缓存: {query_hash}, TTL: {ttl}s")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"缓存响应失败: {e}")
            return False
    
    async def invalidate_cache(self, pattern: Optional[str] = None) -> int:
        """失效缓存"""
        try:
            if pattern:
                # 失效匹配模式的缓存
                full_pattern = f"{self.cache_prefix}{pattern}"
                count = await self.cache_manager.clear_pattern(full_pattern)
            else:
                # 失效所有查询缓存
                count = await self.cache_manager.clear_pattern(f"{self.cache_prefix}*")
            
            self.stats.eviction_count += count
            logger.info(f"缓存失效完成: {count}个条目")
            return count
            
        except Exception as e:
            logger.error(f"缓存失效失败: {e}")
            return 0
    
    async def get_similar_queries(self, query: str, limit: int = 5) -> List[str]:
        """获取相似查询"""
        try:
            # 简单实现：基于关键词匹配
            # 生产环境应使用向量相似度搜索
            
            query_words = set(query.lower().split())
            similar_queries = []
            
            # 从热点查询中查找相似的
            hot_queries = await self.cache_manager.get_list(self.hot_queries_key, 0, 100)
            
            for hot_query in hot_queries:
                if isinstance(hot_query, dict) and 'query' in hot_query:
                    hot_query_text = hot_query['query']
                    hot_words = set(hot_query_text.lower().split())
                    
                    # 计算简单的相似度
                    intersection = len(query_words.intersection(hot_words))
                    union = len(query_words.union(hot_words))
                    similarity = intersection / union if union > 0 else 0
                    
                    if similarity >= self.similarity_threshold:
                        similar_queries.append(hot_query_text)
                        
                        if len(similar_queries) >= limit:
                            break
            
            return similar_queries
            
        except Exception as e:
            logger.error(f"获取相似查询失败: {e}")
            return []
    
    async def preload_cache(self, queries: List[str]) -> int:
        """预加载缓存"""
        try:
            # 这里应该调用RAG系统预先生成响应
            # 暂时只是标记为预加载查询
            
            preload_count = 0
            for query in queries:
                preload_key = f"preload:{self._generate_simple_hash(query)}"
                success = await self.cache_manager.set(
                    preload_key, 
                    {'query': query, 'preloaded': True}, 
                    3600
                )
                if success:
                    preload_count += 1
            
            logger.info(f"预加载缓存完成: {preload_count}个查询")
            return preload_count
            
        except Exception as e:
            logger.error(f"预加载缓存失败: {e}")
            return 0
    
    async def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            # 获取基础统计
            cache_stats = await self.cache_manager.get_statistics()
            
            # 获取热点查询数量
            hot_queries_count = len(await self.cache_manager.get_list(self.hot_queries_key, 0, -1))
            
            return {
                'hit_rate': self.stats.hit_rate,
                'hit_count': self.stats.hit_count,
                'miss_count': self.stats.miss_count,
                'eviction_count': self.stats.eviction_count,
                'hot_queries_count': hot_queries_count,
                'cache_type': cache_stats.get('cache_type', 'unknown'),
                'total_keys': cache_stats.get('total_keys', 0),
                'memory_usage': cache_stats.get('memory_usage', 0)
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def _generate_query_hash(self, request: QueryRequest) -> str:
        """生成查询哈希"""
        # 包含查询文本、类型和主要参数
        hash_content = {
            'query': request.query.strip().lower(),
            'query_type': request.query_type,
            'top_k': request.top_k,
            'filters': request.filters or {}
        }
        
        content_str = json.dumps(hash_content, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content_str.encode()).hexdigest()
    
    def _generate_simple_hash(self, text: str) -> str:
        """生成简单哈希"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def _serialize_cache_entry(self, entry: CacheEntry) -> Dict[str, Any]:
        """序列化缓存条目"""
        return {
            'query_hash': entry.query_hash,
            'query_text': entry.query_text,
            'response': asdict(entry.response),
            'created_at': entry.created_at.isoformat(),
            'access_count': entry.access_count,
            'last_accessed': entry.last_accessed.isoformat() if entry.last_accessed else None
        }
    
    def _deserialize_response(self, cached_data: Dict[str, Any]) -> RAGResponse:
        """反序列化响应"""
        response_data = cached_data['response']
        return RAGResponse(
            response=response_data['response'],
            sources=response_data['sources'],
            confidence=response_data['confidence'],
            context=response_data.get('context', ''),
            metadata=response_data.get('metadata', {})
        )
    
    async def _determine_ttl(self, query: str) -> int:
        """确定TTL"""
        # 检查是否为热点查询
        hot_queries = await self.cache_manager.get_list(self.hot_queries_key, 0, 50)
        
        for hot_query in hot_queries:
            if isinstance(hot_query, dict) and hot_query.get('query') == query:
                return self.hot_query_ttl
        
        return self.default_ttl
    
    async def _update_access_stats(self, query_hash: str):
        """更新访问统计"""
        try:
            stats_key = f"access_stats:{query_hash}"
            await self.cache_manager.increment(stats_key, 1, 86400)  # 24小时过期
        except Exception as e:
            logger.error(f"更新访问统计失败: {e}")
    
    async def _update_cache_stats(self):
        """更新缓存统计"""
        try:
            self.stats.total_entries += 1
            
            # 定期保存统计到缓存
            if self.stats.total_entries % 100 == 0:
                await self.cache_manager.set(
                    self.stats_key,
                    asdict(self.stats),
                    86400
                )
        except Exception as e:
            logger.error(f"更新缓存统计失败: {e}")
    
    async def _check_hot_query(self, query: str):
        """检查热点查询"""
        try:
            # 获取查询访问次数
            query_hash = self._generate_simple_hash(query)
            stats_key = f"access_stats:{query_hash}"
            access_count = await self.cache_manager.get(stats_key) or 0
            
            # 如果访问次数超过阈值，添加到热点查询
            if access_count >= 5:  # 阈值可配置
                hot_query_data = {
                    'query': query,
                    'access_count': access_count,
                    'added_at': datetime.utcnow().isoformat()
                }
                
                await self.cache_manager.push_list(
                    self.hot_queries_key,
                    hot_query_data,
                    ttl=86400
                )
                
                logger.debug(f"添加热点查询: {query}")
                
        except Exception as e:
            logger.error(f"检查热点查询失败: {e}")
