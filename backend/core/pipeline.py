"""
RAG处理流水线 - 核心处理逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from models.query import QueryRequest
from models.response import RAGResponse, RetrievalResult, GenerationResult
from utils.logger import get_logger
from utils.text_processing import TextProcessor

logger = get_logger(__name__)


@dataclass
class PipelineStage:
    """流水线阶段"""
    name: str
    enabled: bool = True
    timeout: float = 30.0
    retry_count: int = 3


class RAGPipeline:
    """
    RAG处理流水线 - 负责查询的完整处理流程
    
    处理流程：
    1. 查询理解和预处理
    2. 查询扩展和优化
    3. 多策略检索执行
    4. 结果融合和重排序
    5. 上下文构建和优化
    6. LLM生成和后处理
    7. 质量控制和验证
    8. 引用生成和格式化
    """
    
    def __init__(self, config):
        self.config = config
        self.text_processor = TextProcessor()
        
        # 定义流水线阶段
        self.stages = {
            'query_understanding': PipelineStage('查询理解', True, 10.0),
            'query_expansion': PipelineStage('查询扩展', True, 15.0),
            'retrieval': PipelineStage('检索执行', True, 30.0),
            'reranking': PipelineStage('结果重排序', True, 20.0),
            'context_building': PipelineStage('上下文构建', True, 10.0),
            'generation': PipelineStage('内容生成', True, 60.0),
            'quality_control': PipelineStage('质量控制', True, 20.0),
            'citation_generation': PipelineStage('引用生成', True, 10.0)
        }
        
        logger.info("RAG处理流水线初始化完成")
    
    async def process(self, request: QueryRequest) -> RAGResponse:
        """
        处理RAG查询请求
        
        Args:
            request: 查询请求
            
        Returns:
            RAGResponse: RAG响应结果
        """
        start_time = datetime.utcnow()
        pipeline_context = {
            'original_query': request.query,
            'query_type': request.query_type,
            'filters': request.filters or {},
            'top_k': request.top_k or self.config.retrieval_top_k,
            'start_time': start_time
        }
        
        try:
            logger.info(f"开始RAG流水线处理: {request.query[:100]}...")
            
            # 1. 查询理解和预处理
            if self.stages['query_understanding'].enabled:
                pipeline_context = await self._query_understanding(pipeline_context)
            
            # 2. 查询扩展和优化
            if self.stages['query_expansion'].enabled:
                pipeline_context = await self._query_expansion(pipeline_context)
            
            # 3. 多策略检索执行
            if self.stages['retrieval'].enabled:
                pipeline_context = await self._retrieval_execution(pipeline_context)
            
            # 4. 结果融合和重排序
            if self.stages['reranking'].enabled:
                pipeline_context = await self._result_reranking(pipeline_context)
            
            # 5. 上下文构建和优化
            if self.stages['context_building'].enabled:
                pipeline_context = await self._context_building(pipeline_context)
            
            # 6. LLM生成和后处理
            if self.stages['generation'].enabled:
                pipeline_context = await self._content_generation(pipeline_context)
            
            # 7. 质量控制和验证
            if self.stages['quality_control'].enabled:
                pipeline_context = await self._quality_control(pipeline_context)
            
            # 8. 引用生成和格式化
            if self.stages['citation_generation'].enabled:
                pipeline_context = await self._citation_generation(pipeline_context)
            
            # 构建最终响应
            response = self._build_response(pipeline_context)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"RAG流水线处理完成, 耗时: {processing_time:.2f}s")
            
            return response
            
        except Exception as e:
            logger.error(f"RAG流水线处理失败: {str(e)}")
            raise
    
    async def _query_understanding(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """查询理解和预处理"""
        logger.debug("执行查询理解阶段")
        
        query = context['original_query']
        
        # 查询清理和标准化
        cleaned_query = self.text_processor.clean_text(query)
        
        # 查询意图识别
        query_intent = await self._identify_query_intent(cleaned_query)
        
        # 实体识别和提取
        entities = await self._extract_entities(cleaned_query)
        
        # 查询复杂度评估
        complexity = self._assess_query_complexity(cleaned_query)
        
        context.update({
            'cleaned_query': cleaned_query,
            'query_intent': query_intent,
            'entities': entities,
            'complexity': complexity
        })
        
        logger.debug(f"查询理解完成: 意图={query_intent}, 复杂度={complexity}")
        return context
    
    async def _query_expansion(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """查询扩展和优化"""
        logger.debug("执行查询扩展阶段")
        
        cleaned_query = context['cleaned_query']
        entities = context['entities']
        
        # 同义词扩展
        synonyms = await self._get_synonyms(cleaned_query)
        
        # 相关术语扩展
        related_terms = await self._get_related_terms(entities)
        
        # 构建扩展查询
        expanded_queries = self._build_expanded_queries(
            cleaned_query, synonyms, related_terms
        )
        
        context.update({
            'synonyms': synonyms,
            'related_terms': related_terms,
            'expanded_queries': expanded_queries
        })
        
        logger.debug(f"查询扩展完成: 生成{len(expanded_queries)}个扩展查询")
        return context
    
    async def _retrieval_execution(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """多策略检索执行"""
        logger.debug("执行检索阶段")
        
        # 这里应该调用实际的检索引擎
        # 暂时使用模拟数据
        retrieval_results = [
            RetrievalResult(
                document_id=f"doc_{i}",
                content=f"模拟检索结果 {i}",
                score=0.9 - i * 0.1,
                metadata={'source': f'document_{i}.pdf'}
            )
            for i in range(context['top_k'])
        ]
        
        context['retrieval_results'] = retrieval_results
        
        logger.debug(f"检索执行完成: 获得{len(retrieval_results)}个结果")
        return context
    
    async def _result_reranking(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """结果融合和重排序"""
        logger.debug("执行结果重排序阶段")
        
        retrieval_results = context['retrieval_results']
        query = context['cleaned_query']
        
        # 重排序逻辑 - 基于相关性和质量分数
        reranked_results = sorted(
            retrieval_results,
            key=lambda x: self._calculate_relevance_score(x, query),
            reverse=True
        )
        
        context['reranked_results'] = reranked_results
        
        logger.debug(f"结果重排序完成: 重排序{len(reranked_results)}个结果")
        return context
    
    async def _context_building(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """上下文构建和优化"""
        logger.debug("执行上下文构建阶段")
        
        reranked_results = context['reranked_results']
        
        # 构建上下文
        context_parts = []
        total_length = 0
        
        for result in reranked_results:
            if total_length + len(result.content) <= self.config.max_context_length:
                context_parts.append(result.content)
                total_length += len(result.content)
            else:
                break
        
        built_context = '\n\n'.join(context_parts)
        
        context.update({
            'built_context': built_context,
            'context_length': total_length,
            'used_sources': reranked_results[:len(context_parts)]
        })
        
        logger.debug(f"上下文构建完成: 长度={total_length}, 使用{len(context_parts)}个源")
        return context
    
    async def _content_generation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """LLM生成和后处理"""
        logger.debug("执行内容生成阶段")
        
        # 这里应该调用实际的生成引擎
        # 暂时使用模拟响应
        generated_content = f"基于检索到的信息，针对查询'{context['original_query']}'的回答：\n\n这是一个模拟的生成响应。"
        
        context['generated_content'] = generated_content
        
        logger.debug("内容生成完成")
        return context
    
    async def _quality_control(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """质量控制和验证"""
        logger.debug("执行质量控制阶段")
        
        generated_content = context['generated_content']
        
        # 幻觉检测
        hallucination_score = await self._detect_hallucination(
            generated_content, context['built_context']
        )
        
        # 质量评分
        quality_score = self._calculate_quality_score(generated_content)
        
        # 置信度计算
        confidence = self._calculate_confidence(
            context['reranked_results'], quality_score, hallucination_score
        )
        
        context.update({
            'hallucination_score': hallucination_score,
            'quality_score': quality_score,
            'confidence': confidence
        })
        
        logger.debug(f"质量控制完成: 质量分={quality_score:.2f}, 置信度={confidence:.2f}")
        return context
    
    async def _citation_generation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """引用生成和格式化"""
        logger.debug("执行引用生成阶段")
        
        if self.config.enable_citation_generation:
            used_sources = context['used_sources']
            citations = [
                {
                    'id': i + 1,
                    'source': source.metadata.get('source', f'Document {i+1}'),
                    'content_snippet': source.content[:100] + '...',
                    'relevance_score': source.score
                }
                for i, source in enumerate(used_sources)
            ]
            
            context['citations'] = citations
            logger.debug(f"引用生成完成: 生成{len(citations)}个引用")
        else:
            context['citations'] = []
        
        return context
    
    def _build_response(self, context: Dict[str, Any]) -> RAGResponse:
        """构建最终响应"""
        processing_time = (datetime.utcnow() - context['start_time']).total_seconds()
        
        return RAGResponse(
            response=context['generated_content'],
            sources=context.get('citations', []),
            confidence=context.get('confidence', 0.8),
            context=context['built_context'],
            metadata={
                'processing_time': processing_time,
                'query_intent': context.get('query_intent'),
                'complexity': context.get('complexity'),
                'retrieval_count': len(context.get('retrieval_results', [])),
                'context_length': context.get('context_length', 0),
                'quality_score': context.get('quality_score', 0.8),
                'hallucination_score': context.get('hallucination_score', 0.1)
            }
        )
    
    # 辅助方法
    async def _identify_query_intent(self, query: str) -> str:
        """识别查询意图"""
        # 简单的意图识别逻辑
        if any(word in query.lower() for word in ['风险', '问题', '隐患']):
            return 'risk_assessment'
        elif any(word in query.lower() for word in ['条款', '内容', '规定']):
            return 'content_analysis'
        elif any(word in query.lower() for word in ['建议', '优化', '改进']):
            return 'recommendation'
        else:
            return 'general_query'
    
    async def _extract_entities(self, query: str) -> List[str]:
        """提取实体"""
        # 简单的实体提取逻辑
        entities = []
        # 这里应该使用NER模型
        return entities
    
    def _assess_query_complexity(self, query: str) -> str:
        """评估查询复杂度"""
        word_count = len(query.split())
        if word_count < 5:
            return 'simple'
        elif word_count < 15:
            return 'medium'
        else:
            return 'complex'
    
    async def _get_synonyms(self, query: str) -> List[str]:
        """获取同义词"""
        # 这里应该调用同义词服务
        return []
    
    async def _get_related_terms(self, entities: List[str]) -> List[str]:
        """获取相关术语"""
        # 这里应该调用知识图谱服务
        return []
    
    def _build_expanded_queries(self, original: str, synonyms: List[str], 
                               related_terms: List[str]) -> List[str]:
        """构建扩展查询"""
        expanded = [original]
        # 简单的查询扩展逻辑
        return expanded
    
    def _calculate_relevance_score(self, result: RetrievalResult, query: str) -> float:
        """计算相关性分数"""
        # 简单的相关性计算
        return result.score
    
    async def _detect_hallucination(self, content: str, context: str) -> float:
        """检测幻觉"""
        # 简单的幻觉检测逻辑
        return 0.1  # 低幻觉分数
    
    def _calculate_quality_score(self, content: str) -> float:
        """计算质量分数"""
        # 简单的质量评分逻辑
        return 0.8
    
    def _calculate_confidence(self, results: List[RetrievalResult], 
                            quality_score: float, hallucination_score: float) -> float:
        """计算置信度"""
        if not results:
            return 0.0
        
        avg_retrieval_score = sum(r.score for r in results) / len(results)
        confidence = (avg_retrieval_score + quality_score) / 2 * (1 - hallucination_score)
        return min(max(confidence, 0.0), 1.0)
