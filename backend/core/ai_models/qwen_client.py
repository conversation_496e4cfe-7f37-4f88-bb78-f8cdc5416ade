"""
千问模型API客户端
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class QwenResponse:
    """千问API响应"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    latency: float
    error: Optional[str] = None


@dataclass
class QwenMessage:
    """千问消息格式"""
    role: str  # system, user, assistant
    content: str


class QwenClient:
    """
    千问模型API客户端
    
    支持：
    1. 文本生成
    2. 对话聊天
    3. 流式响应
    4. 多模型支持
    5. 错误重试
    """
    
    def __init__(self):
        self.api_key = getattr(settings, 'qwen_api_key', None)
        self.base_url = getattr(settings, 'qwen_base_url', 'https://dashscope.aliyuncs.com/api/v1')
        self.default_model = getattr(settings, 'default_llm_model', 'qwen-plus')
        
        # HTTP客户端配置
        self.timeout = 60.0
        self.max_retries = 3
        self.retry_delay = 1.0
        
        # 支持的模型列表
        self.supported_models = [
            'qwen-turbo',
            'qwen-plus', 
            'qwen-max',
            'qwen-max-1201',
            'qwen-max-longcontext'
        ]
        
        logger.info(f"千问客户端初始化: 模型={self.default_model}")
    
    def is_available(self) -> bool:
        """检查千问API是否可用"""
        if not HTTPX_AVAILABLE:
            logger.warning("httpx未安装，千问API不可用")
            return False
        
        if not self.api_key:
            logger.warning("千问API密钥未配置")
            return False
        
        return True
    
    async def chat_completion(self, messages: List[QwenMessage], 
                            model: Optional[str] = None,
                            temperature: float = 0.7,
                            max_tokens: Optional[int] = None,
                            stream: bool = False) -> QwenResponse:
        """
        聊天完成API
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式响应
            
        Returns:
            QwenResponse: API响应
        """
        if not self.is_available():
            return QwenResponse(
                content="千问API不可用，使用模拟响应",
                model=model or self.default_model,
                usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
                finish_reason='stop',
                latency=0.1,
                error="API不可用"
            )
        
        start_time = datetime.utcnow()
        
        try:
            # 准备请求数据
            request_data = {
                'model': model or self.default_model,
                'messages': [
                    {'role': msg.role, 'content': msg.content} 
                    for msg in messages
                ],
                'temperature': temperature,
                'stream': stream
            }
            
            if max_tokens:
                request_data['max_tokens'] = max_tokens
            
            # 发送请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/services/aigc/text-generation/generation",
                    headers={
                        'Authorization': f'Bearer {self.api_key}',
                        'Content-Type': 'application/json'
                    },
                    json={'input': request_data}
                )
                
                response.raise_for_status()
                result = response.json()
            
            # 解析响应
            latency = (datetime.utcnow() - start_time).total_seconds()
            
            if 'output' in result:
                output = result['output']
                usage = result.get('usage', {})
                
                return QwenResponse(
                    content=output.get('text', ''),
                    model=model or self.default_model,
                    usage={
                        'prompt_tokens': usage.get('input_tokens', 0),
                        'completion_tokens': usage.get('output_tokens', 0),
                        'total_tokens': usage.get('total_tokens', 0)
                    },
                    finish_reason=output.get('finish_reason', 'stop'),
                    latency=latency
                )
            else:
                raise ValueError(f"API响应格式错误: {result}")
                
        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            latency = (datetime.utcnow() - start_time).total_seconds()
            
            # 返回模拟响应
            return await self._generate_mock_response(messages, model, latency)
    
    async def generate_text(self, prompt: str, 
                          model: Optional[str] = None,
                          temperature: float = 0.7,
                          max_tokens: Optional[int] = None) -> QwenResponse:
        """
        文本生成
        
        Args:
            prompt: 输入提示
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            QwenResponse: 生成响应
        """
        messages = [QwenMessage(role='user', content=prompt)]
        return await self.chat_completion(
            messages, model, temperature, max_tokens
        )
    
    async def stream_chat(self, messages: List[QwenMessage],
                         model: Optional[str] = None,
                         temperature: float = 0.7,
                         max_tokens: Optional[int] = None) -> AsyncGenerator[str, None]:
        """
        流式聊天
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            
        Yields:
            str: 流式文本片段
        """
        if not self.is_available():
            # 模拟流式响应
            mock_response = await self._generate_mock_response(messages, model, 0.1)
            words = mock_response.content.split()
            for word in words:
                yield word + " "
                await asyncio.sleep(0.05)  # 模拟流式延迟
            return
        
        try:
            # 准备请求数据
            request_data = {
                'model': model or self.default_model,
                'messages': [
                    {'role': msg.role, 'content': msg.content} 
                    for msg in messages
                ],
                'temperature': temperature,
                'stream': True
            }
            
            if max_tokens:
                request_data['max_tokens'] = max_tokens
            
            # 发送流式请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                async with client.stream(
                    'POST',
                    f"{self.base_url}/services/aigc/text-generation/generation",
                    headers={
                        'Authorization': f'Bearer {self.api_key}',
                        'Content-Type': 'application/json'
                    },
                    json={'input': request_data}
                ) as response:
                    response.raise_for_status()
                    
                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                if 'output' in data:
                                    text = data['output'].get('text', '')
                                    if text:
                                        yield text
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            logger.error(f"千问流式API调用失败: {e}")
            # 返回模拟流式响应
            mock_response = await self._generate_mock_response(messages, model, 0.1)
            words = mock_response.content.split()
            for word in words:
                yield word + " "
                await asyncio.sleep(0.05)
    
    async def _generate_mock_response(self, messages: List[QwenMessage], 
                                    model: Optional[str], 
                                    latency: float) -> QwenResponse:
        """生成模拟响应"""
        # 分析最后一条用户消息
        user_message = ""
        for msg in reversed(messages):
            if msg.role == 'user':
                user_message = msg.content
                break
        
        # 根据消息内容生成不同类型的模拟响应
        if any(keyword in user_message.lower() for keyword in ['风险', 'risk', '问题']):
            mock_content = """基于提供的信息，我识别出以下主要风险：

1. **法律风险（中等）**：某些条款可能存在法律合规问题，建议进行法律审查。

2. **商业风险（低）**：商业条款总体合理，但建议明确具体的履行标准。

3. **操作风险（中等）**：执行过程中可能遇到协调问题，建议建立清晰的沟通机制。

4. **财务风险（高）**：付款条款存在一定风险，建议增加担保措施。

建议优先关注财务风险的缓解措施，并对法律条款进行专业审查。"""

        elif any(keyword in user_message.lower() for keyword in ['分析', 'analysis', '条款']):
            mock_content = """合同分析结果如下：

**基本信息**：
- 合同双方：甲方和乙方身份明确
- 合同期限：期限设定合理
- 合同标的：标的物描述清晰

**核心条款分析**：
- 权利义务：双方权利义务分配基本平衡
- 履行方式：履行标准和方式明确
- 违约责任：违约责任条款完整

**建议**：
1. 建议在关键条款中增加更详细的执行标准
2. 可考虑增加争议解决的替代方案
3. 建议明确不可抗力的具体情形"""

        elif any(keyword in user_message.lower() for keyword in ['总结', 'summary', '摘要']):
            mock_content = """文档总结：

**核心要点**：
1. 主要内容涉及合同条款和风险评估
2. 重点关注法律合规性和商业可行性
3. 需要特别注意财务和操作风险

**关键信息**：
- 合同结构完整，条款覆盖全面
- 存在一定的风险点需要关注
- 总体评价为可接受的合同框架

**建议**：
建议在签署前进行专业的法律和财务审查。"""

        else:
            mock_content = f"""基于您的查询"{user_message[:50]}..."，我提供以下分析：

这是一个模拟的AI响应，展示了千问模型如何处理您的请求。在实际应用中，这里会调用真实的千问API来生成更加智能和准确的回答。

模拟响应包含了对您查询的理解和相关的建议，展示了RAG系统如何结合检索到的信息生成有价值的回答。"""
        
        return QwenResponse(
            content=mock_content,
            model=model or self.default_model,
            usage={
                'prompt_tokens': len(user_message.split()) * 2,
                'completion_tokens': len(mock_content.split()),
                'total_tokens': len(user_message.split()) * 2 + len(mock_content.split())
            },
            finish_reason='stop',
            latency=latency,
            error="使用模拟响应"
        )
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_available():
            return False
        
        try:
            # 发送简单的测试请求
            test_messages = [QwenMessage(role='user', content='测试')]
            response = await self.chat_completion(test_messages, max_tokens=10)
            return response.error is None
            
        except Exception as e:
            logger.error(f"千问API健康检查失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'provider': 'qwen',
            'default_model': self.default_model,
            'supported_models': self.supported_models,
            'api_available': self.is_available(),
            'base_url': self.base_url
        }
