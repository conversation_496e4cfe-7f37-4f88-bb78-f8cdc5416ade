"""
嵌入模型客户端
支持多种嵌入模型API
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

import numpy as np
from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class EmbeddingResponse:
    """嵌入响应"""
    embeddings: List[List[float]]
    model: str
    usage: Dict[str, int]
    latency: float
    error: Optional[str] = None


class EmbeddingClient:
    """
    嵌入模型客户端
    
    支持：
    1. 文本嵌入生成
    2. 批量嵌入处理
    3. 多种嵌入模型
    4. 缓存优化
    5. 错误重试
    """
    
    def __init__(self):
        self.qwen_api_key = getattr(settings, 'qwen_api_key', None)
        self.openai_api_key = getattr(settings, 'openai_api_key', None)
        self.qwen_base_url = getattr(settings, 'qwen_base_url', 'https://dashscope.aliyuncs.com/api/v1')
        self.openai_base_url = getattr(settings, 'openai_base_url', 'https://api.openai.com/v1')
        
        self.default_model = getattr(settings, 'embedding_model', 'text-embedding-v1')
        self.embedding_dimensions = getattr(settings, 'embedding_dimensions', 1536)
        self.batch_size = getattr(settings, 'embedding_batch_size', 100)
        
        # HTTP客户端配置
        self.timeout = 30.0
        self.max_retries = 3
        
        # 支持的模型映射
        self.model_providers = {
            # 千问嵌入模型
            'text-embedding-v1': 'qwen',
            'text-embedding-v2': 'qwen',
            # OpenAI嵌入模型
            'text-embedding-3-small': 'openai',
            'text-embedding-3-large': 'openai',
            'text-embedding-ada-002': 'openai'
        }
        
        # 嵌入缓存
        self._embedding_cache: Dict[str, List[float]] = {}
        
        logger.info(f"嵌入客户端初始化: 模型={self.default_model}")
    
    def is_available(self, provider: str = 'auto') -> bool:
        """检查嵌入API是否可用"""
        if not HTTPX_AVAILABLE:
            logger.warning("httpx未安装，嵌入API不可用")
            return False
        
        if provider == 'auto':
            provider = self.model_providers.get(self.default_model, 'qwen')
        
        if provider == 'qwen':
            return bool(self.qwen_api_key)
        elif provider == 'openai':
            return bool(self.openai_api_key)
        
        return False
    
    async def create_embeddings(self, texts: Union[str, List[str]], 
                              model: Optional[str] = None) -> EmbeddingResponse:
        """
        创建文本嵌入
        
        Args:
            texts: 文本或文本列表
            model: 嵌入模型名称
            
        Returns:
            EmbeddingResponse: 嵌入响应
        """
        if isinstance(texts, str):
            texts = [texts]
        
        model = model or self.default_model
        provider = self.model_providers.get(model, 'qwen')
        
        if not self.is_available(provider):
            logger.warning(f"嵌入API不可用，使用模拟嵌入: provider={provider}")
            return await self._generate_mock_embeddings(texts, model)
        
        start_time = datetime.utcnow()
        
        try:
            # 检查缓存
            cached_embeddings = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                cache_key = self._generate_cache_key(text, model)
                if cache_key in self._embedding_cache:
                    cached_embeddings.append((i, self._embedding_cache[cache_key]))
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
            
            # 处理未缓存的文本
            if uncached_texts:
                if provider == 'qwen':
                    response = await self._create_qwen_embeddings(uncached_texts, model)
                elif provider == 'openai':
                    response = await self._create_openai_embeddings(uncached_texts, model)
                else:
                    raise ValueError(f"不支持的嵌入提供商: {provider}")
                
                if response.error:
                    return response
                
                # 缓存新的嵌入
                for i, embedding in enumerate(response.embeddings):
                    text = uncached_texts[i]
                    cache_key = self._generate_cache_key(text, model)
                    self._embedding_cache[cache_key] = embedding
            else:
                response = EmbeddingResponse(
                    embeddings=[],
                    model=model,
                    usage={'total_tokens': 0},
                    latency=0.0
                )
            
            # 合并缓存和新生成的嵌入
            all_embeddings = [None] * len(texts)
            
            # 填入缓存的嵌入
            for i, embedding in cached_embeddings:
                all_embeddings[i] = embedding
            
            # 填入新生成的嵌入
            for i, embedding in enumerate(response.embeddings):
                original_index = uncached_indices[i]
                all_embeddings[original_index] = embedding
            
            latency = (datetime.utcnow() - start_time).total_seconds()
            
            return EmbeddingResponse(
                embeddings=all_embeddings,
                model=model,
                usage=response.usage,
                latency=latency
            )
            
        except Exception as e:
            logger.error(f"创建嵌入失败: {e}")
            return await self._generate_mock_embeddings(texts, model)
    
    async def _create_qwen_embeddings(self, texts: List[str], model: str) -> EmbeddingResponse:
        """创建千问嵌入"""
        try:
            request_data = {
                'model': model,
                'input': {
                    'texts': texts
                }
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.qwen_base_url}/services/embeddings/text-embedding/text-embedding",
                    headers={
                        'Authorization': f'Bearer {self.qwen_api_key}',
                        'Content-Type': 'application/json'
                    },
                    json=request_data
                )
                
                response.raise_for_status()
                result = response.json()
            
            if 'output' in result:
                output = result['output']
                embeddings = output.get('embeddings', [])
                usage = result.get('usage', {})
                
                return EmbeddingResponse(
                    embeddings=[emb.get('embedding', []) for emb in embeddings],
                    model=model,
                    usage={
                        'total_tokens': usage.get('total_tokens', 0)
                    },
                    latency=0.0
                )
            else:
                raise ValueError(f"千问嵌入API响应格式错误: {result}")
                
        except Exception as e:
            logger.error(f"千问嵌入API调用失败: {e}")
            raise
    
    async def _create_openai_embeddings(self, texts: List[str], model: str) -> EmbeddingResponse:
        """创建OpenAI嵌入"""
        try:
            request_data = {
                'model': model,
                'input': texts
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.openai_base_url}/embeddings",
                    headers={
                        'Authorization': f'Bearer {self.openai_api_key}',
                        'Content-Type': 'application/json'
                    },
                    json=request_data
                )
                
                response.raise_for_status()
                result = response.json()
            
            if 'data' in result:
                embeddings = [item['embedding'] for item in result['data']]
                usage = result.get('usage', {})
                
                return EmbeddingResponse(
                    embeddings=embeddings,
                    model=model,
                    usage={
                        'total_tokens': usage.get('total_tokens', 0)
                    },
                    latency=0.0
                )
            else:
                raise ValueError(f"OpenAI嵌入API响应格式错误: {result}")
                
        except Exception as e:
            logger.error(f"OpenAI嵌入API调用失败: {e}")
            raise
    
    async def _generate_mock_embeddings(self, texts: List[str], model: str) -> EmbeddingResponse:
        """生成模拟嵌入"""
        embeddings = []
        
        for text in texts:
            # 使用文本哈希生成确定性的模拟向量
            text_hash = hashlib.md5(text.encode()).hexdigest()
            
            # 生成指定维度的向量
            embedding = []
            for i in range(self.embedding_dimensions):
                # 使用哈希值的不同部分生成向量分量
                hash_part = text_hash[(i % len(text_hash))]
                value = (ord(hash_part) - 48) / 10.0 - 0.5  # 归一化到[-0.5, 0.5]
                embedding.append(value)
            
            # 归一化向量
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = [x / norm for x in embedding]
            
            embeddings.append(embedding)
        
        return EmbeddingResponse(
            embeddings=embeddings,
            model=model,
            usage={
                'total_tokens': sum(len(text.split()) for text in texts)
            },
            latency=0.1,
            error="使用模拟嵌入"
        )
    
    async def batch_create_embeddings(self, texts: List[str], 
                                    model: Optional[str] = None,
                                    batch_size: Optional[int] = None) -> EmbeddingResponse:
        """
        批量创建嵌入
        
        Args:
            texts: 文本列表
            model: 嵌入模型名称
            batch_size: 批处理大小
            
        Returns:
            EmbeddingResponse: 嵌入响应
        """
        batch_size = batch_size or self.batch_size
        model = model or self.default_model
        
        if len(texts) <= batch_size:
            return await self.create_embeddings(texts, model)
        
        # 分批处理
        all_embeddings = []
        total_usage = {'total_tokens': 0}
        total_latency = 0.0
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_response = await self.create_embeddings(batch_texts, model)
            
            if batch_response.error:
                return batch_response
            
            all_embeddings.extend(batch_response.embeddings)
            total_usage['total_tokens'] += batch_response.usage.get('total_tokens', 0)
            total_latency += batch_response.latency
            
            # 避免API限流
            if i + batch_size < len(texts):
                await asyncio.sleep(0.1)
        
        return EmbeddingResponse(
            embeddings=all_embeddings,
            model=model,
            usage=total_usage,
            latency=total_latency
        )
    
    def _generate_cache_key(self, text: str, model: str) -> str:
        """生成缓存键"""
        content = f"{model}:{text}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 测试嵌入生成
            test_response = await self.create_embeddings("测试文本")
            return len(test_response.embeddings) > 0
            
        except Exception as e:
            logger.error(f"嵌入客户端健康检查失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        available_providers = []
        if self.is_available('qwen'):
            available_providers.append('qwen')
        if self.is_available('openai'):
            available_providers.append('openai')
        
        return {
            'default_model': self.default_model,
            'embedding_dimensions': self.embedding_dimensions,
            'batch_size': self.batch_size,
            'supported_models': list(self.model_providers.keys()),
            'available_providers': available_providers,
            'cache_size': len(self._embedding_cache)
        }
    
    def clear_cache(self):
        """清空嵌入缓存"""
        self._embedding_cache.clear()
        logger.info("嵌入缓存已清空")
