"""
检索引擎模块 - 多策略检索和结果融合
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import math
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict

from models.response import RetrievalResult
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SearchStrategy:
    """搜索策略配置"""
    name: str
    weight: float = 1.0
    enabled: bool = True
    top_k: int = 20
    parameters: Dict[str, Any] = None


@dataclass
class RetrievalConfig:
    """检索配置"""
    dense_retrieval: SearchStrategy
    sparse_retrieval: SearchStrategy
    semantic_retrieval: SearchStrategy
    fusion_method: str = 'rrf'  # reciprocal_rank_fusion
    rerank_enabled: bool = True
    rerank_top_k: int = 100


class DenseRetriever:
    """密集检索器 - 基于向量相似度"""
    
    def __init__(self):
        self.index_built = False
        logger.debug("密集检索器初始化完成")
    
    async def search(self, query: str, top_k: int = 10) -> List[RetrievalResult]:
        """
        执行密集检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            List[RetrievalResult]: 检索结果
        """
        try:
            logger.debug(f"执行密集检索: {query[:50]}...")
            
            # 这里应该调用实际的向量检索
            # 暂时使用模拟结果
            results = []
            for i in range(top_k):
                result = RetrievalResult(
                    document_id=f"dense_doc_{i}",
                    content=f"密集检索结果 {i}: 与查询'{query[:20]}'相关的内容",
                    score=0.95 - i * 0.05,
                    metadata={
                        'retrieval_method': 'dense',
                        'vector_similarity': 0.95 - i * 0.05,
                        'source': f'document_{i}.pdf'
                    }
                )
                results.append(result)
            
            logger.debug(f"密集检索完成: 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            logger.error(f"密集检索失败: {str(e)}")
            return []
    
    async def build_index(self, documents: List[Dict]) -> bool:
        """构建向量索引"""
        try:
            logger.info("开始构建密集检索索引")
            # 这里应该构建实际的向量索引
            self.index_built = True
            logger.info("密集检索索引构建完成")
            return True
        except Exception as e:
            logger.error(f"构建密集检索索引失败: {str(e)}")
            return False


class SparseRetriever:
    """稀疏检索器 - 基于关键词匹配"""
    
    def __init__(self):
        self.index_built = False
        logger.debug("稀疏检索器初始化完成")
    
    async def search(self, query: str, top_k: int = 10) -> List[RetrievalResult]:
        """
        执行稀疏检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            List[RetrievalResult]: 检索结果
        """
        try:
            logger.debug(f"执行稀疏检索: {query[:50]}...")
            
            # 这里应该调用实际的关键词检索
            # 暂时使用模拟结果
            results = []
            for i in range(top_k):
                result = RetrievalResult(
                    document_id=f"sparse_doc_{i}",
                    content=f"稀疏检索结果 {i}: 包含关键词'{query[:20]}'的内容",
                    score=0.90 - i * 0.04,
                    metadata={
                        'retrieval_method': 'sparse',
                        'keyword_match_score': 0.90 - i * 0.04,
                        'matched_terms': query.split()[:3],
                        'source': f'document_{i+10}.pdf'
                    }
                )
                results.append(result)
            
            logger.debug(f"稀疏检索完成: 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            logger.error(f"稀疏检索失败: {str(e)}")
            return []
    
    async def build_index(self, documents: List[Dict]) -> bool:
        """构建关键词索引"""
        try:
            logger.info("开始构建稀疏检索索引")
            # 这里应该构建实际的关键词索引
            self.index_built = True
            logger.info("稀疏检索索引构建完成")
            return True
        except Exception as e:
            logger.error(f"构建稀疏检索索引失败: {str(e)}")
            return False


class SemanticRetriever:
    """语义检索器 - 基于语义理解"""
    
    def __init__(self):
        self.index_built = False
        logger.debug("语义检索器初始化完成")
    
    async def search(self, query: str, top_k: int = 10) -> List[RetrievalResult]:
        """
        执行语义检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            List[RetrievalResult]: 检索结果
        """
        try:
            logger.debug(f"执行语义检索: {query[:50]}...")
            
            # 这里应该调用实际的语义检索
            # 暂时使用模拟结果
            results = []
            for i in range(top_k):
                result = RetrievalResult(
                    document_id=f"semantic_doc_{i}",
                    content=f"语义检索结果 {i}: 语义相关于'{query[:20]}'的内容",
                    score=0.88 - i * 0.03,
                    metadata={
                        'retrieval_method': 'semantic',
                        'semantic_similarity': 0.88 - i * 0.03,
                        'semantic_concepts': ['合同', '风险', '条款'],
                        'source': f'document_{i+20}.pdf'
                    }
                )
                results.append(result)
            
            logger.debug(f"语义检索完成: 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            logger.error(f"语义检索失败: {str(e)}")
            return []
    
    async def build_index(self, documents: List[Dict]) -> bool:
        """构建语义索引"""
        try:
            logger.info("开始构建语义检索索引")
            # 这里应该构建实际的语义索引
            self.index_built = True
            logger.info("语义检索索引构建完成")
            return True
        except Exception as e:
            logger.error(f"构建语义检索索引失败: {str(e)}")
            return False


class ResultFusion:
    """结果融合器"""
    
    def __init__(self):
        logger.debug("结果融合器初始化完成")
    
    def fuse_results(self, results_list: List[List[RetrievalResult]], 
                    method: str = 'rrf', weights: Optional[List[float]] = None) -> List[RetrievalResult]:
        """
        融合多个检索结果
        
        Args:
            results_list: 多个检索结果列表
            method: 融合方法 ('rrf', 'weighted_sum', 'max_score')
            weights: 各检索器权重
            
        Returns:
            List[RetrievalResult]: 融合后的结果
        """
        if not results_list:
            return []
        
        if method == 'rrf':
            return self._reciprocal_rank_fusion(results_list)
        elif method == 'weighted_sum':
            return self._weighted_sum_fusion(results_list, weights)
        elif method == 'max_score':
            return self._max_score_fusion(results_list)
        else:
            logger.warning(f"未知的融合方法: {method}, 使用RRF")
            return self._reciprocal_rank_fusion(results_list)
    
    def _reciprocal_rank_fusion(self, results_list: List[List[RetrievalResult]], 
                               k: int = 60) -> List[RetrievalResult]:
        """倒数排名融合 (Reciprocal Rank Fusion)"""
        doc_scores = defaultdict(float)
        doc_results = {}
        
        for results in results_list:
            for rank, result in enumerate(results, 1):
                doc_id = result.document_id
                rrf_score = 1.0 / (k + rank)
                doc_scores[doc_id] += rrf_score
                
                # 保存结果对象（使用第一次出现的）
                if doc_id not in doc_results:
                    doc_results[doc_id] = result
        
        # 按融合分数排序
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建融合结果
        fused_results = []
        for doc_id, score in sorted_docs:
            result = doc_results[doc_id]
            # 更新分数为融合分数
            fused_result = RetrievalResult(
                document_id=result.document_id,
                content=result.content,
                score=score,
                metadata={
                    **result.metadata,
                    'fusion_method': 'rrf',
                    'original_score': result.score,
                    'fusion_score': score
                }
            )
            fused_results.append(fused_result)
        
        logger.debug(f"RRF融合完成: {len(fused_results)}个结果")
        return fused_results
    
    def _weighted_sum_fusion(self, results_list: List[List[RetrievalResult]], 
                           weights: Optional[List[float]] = None) -> List[RetrievalResult]:
        """加权求和融合"""
        if weights is None:
            weights = [1.0] * len(results_list)
        
        doc_scores = defaultdict(float)
        doc_results = {}
        
        for i, results in enumerate(results_list):
            weight = weights[i] if i < len(weights) else 1.0
            for result in results:
                doc_id = result.document_id
                doc_scores[doc_id] += result.score * weight
                
                if doc_id not in doc_results:
                    doc_results[doc_id] = result
        
        # 按融合分数排序
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建融合结果
        fused_results = []
        for doc_id, score in sorted_docs:
            result = doc_results[doc_id]
            fused_result = RetrievalResult(
                document_id=result.document_id,
                content=result.content,
                score=score,
                metadata={
                    **result.metadata,
                    'fusion_method': 'weighted_sum',
                    'original_score': result.score,
                    'fusion_score': score
                }
            )
            fused_results.append(fused_result)
        
        logger.debug(f"加权求和融合完成: {len(fused_results)}个结果")
        return fused_results
    
    def _max_score_fusion(self, results_list: List[List[RetrievalResult]]) -> List[RetrievalResult]:
        """最大分数融合"""
        doc_scores = defaultdict(float)
        doc_results = {}
        
        for results in results_list:
            for result in results:
                doc_id = result.document_id
                if result.score > doc_scores[doc_id]:
                    doc_scores[doc_id] = result.score
                    doc_results[doc_id] = result
        
        # 按分数排序
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建融合结果
        fused_results = []
        for doc_id, score in sorted_docs:
            result = doc_results[doc_id]
            fused_result = RetrievalResult(
                document_id=result.document_id,
                content=result.content,
                score=score,
                metadata={
                    **result.metadata,
                    'fusion_method': 'max_score',
                    'fusion_score': score
                }
            )
            fused_results.append(fused_result)
        
        logger.debug(f"最大分数融合完成: {len(fused_results)}个结果")
        return fused_results


class RetrievalEngine:
    """
    检索引擎 - 负责多策略检索和结果融合
    
    主要职责：
    1. 多策略检索执行
    2. 结果融合和重排序
    3. 相关性评分
    4. 检索缓存管理
    """
    
    def __init__(self, config: Optional[RetrievalConfig] = None):
        # 默认配置
        if config is None:
            config = RetrievalConfig(
                dense_retrieval=SearchStrategy('dense', 1.0, True, 20),
                sparse_retrieval=SearchStrategy('sparse', 0.8, True, 20),
                semantic_retrieval=SearchStrategy('semantic', 0.9, True, 20)
            )
        
        self.config = config
        
        # 初始化检索器
        self.dense_retriever = DenseRetriever()
        self.sparse_retriever = SparseRetriever()
        self.semantic_retriever = SemanticRetriever()
        
        # 初始化融合器
        self.result_fusion = ResultFusion()
        
        # 缓存
        self._search_cache: Dict[str, List[RetrievalResult]] = {}
        
        logger.info("检索引擎初始化完成")
    
    async def search(self, query: str, top_k: int = 10, 
                    filters: Optional[Dict] = None) -> List[RetrievalResult]:
        """
        执行混合检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            filters: 过滤条件
            
        Returns:
            List[RetrievalResult]: 检索结果
        """
        try:
            logger.info(f"开始混合检索: {query[:50]}...")
            start_time = datetime.utcnow()
            
            # 检查缓存
            cache_key = self._generate_cache_key(query, top_k, filters)
            if cache_key in self._search_cache:
                logger.debug("检索缓存命中")
                return self._search_cache[cache_key][:top_k]
            
            # 并行执行多种检索策略
            search_tasks = []
            
            if self.config.dense_retrieval.enabled:
                search_tasks.append(
                    self.dense_retriever.search(query, self.config.dense_retrieval.top_k)
                )
            
            if self.config.sparse_retrieval.enabled:
                search_tasks.append(
                    self.sparse_retriever.search(query, self.config.sparse_retrieval.top_k)
                )
            
            if self.config.semantic_retrieval.enabled:
                search_tasks.append(
                    self.semantic_retriever.search(query, self.config.semantic_retrieval.top_k)
                )
            
            # 等待所有检索完成
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = []
            for result in search_results:
                if isinstance(result, Exception):
                    logger.error(f"检索任务失败: {str(result)}")
                else:
                    valid_results.append(result)
            
            if not valid_results:
                logger.warning("所有检索策略都失败了")
                return []
            
            # 结果融合
            fused_results = self.result_fusion.fuse_results(
                valid_results, 
                method=self.config.fusion_method,
                weights=[
                    self.config.dense_retrieval.weight,
                    self.config.sparse_retrieval.weight,
                    self.config.semantic_retrieval.weight
                ]
            )
            
            # 应用过滤器
            if filters:
                fused_results = self._apply_filters(fused_results, filters)
            
            # 重排序
            if self.config.rerank_enabled:
                fused_results = await self._rerank_results(fused_results, query)
            
            # 缓存结果
            self._search_cache[cache_key] = fused_results
            
            # 返回top_k结果
            final_results = fused_results[:top_k]
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"混合检索完成: 返回{len(final_results)}个结果, 耗时: {processing_time:.2f}s")
            
            return final_results
            
        except Exception as e:
            logger.error(f"混合检索失败: {str(e)}")
            return []
    
    async def build_indexes(self, documents: List[Dict]) -> bool:
        """构建所有索引"""
        try:
            logger.info("开始构建检索索引")
            
            tasks = [
                self.dense_retriever.build_index(documents),
                self.sparse_retriever.build_index(documents),
                self.semantic_retriever.build_index(documents)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = sum(1 for r in results if r is True)
            logger.info(f"索引构建完成: {success_count}/{len(tasks)}个成功")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"构建检索索引失败: {str(e)}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 执行简单的检索测试
            test_results = await self.search("测试查询", top_k=1)
            return True
        except Exception as e:
            logger.error(f"检索引擎健康检查失败: {str(e)}")
            return False
    
    def _generate_cache_key(self, query: str, top_k: int, 
                          filters: Optional[Dict]) -> str:
        """生成缓存键"""
        import hashlib
        content = f"{query}_{top_k}_{str(filters)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _apply_filters(self, results: List[RetrievalResult], 
                      filters: Dict) -> List[RetrievalResult]:
        """应用过滤条件"""
        filtered_results = []
        
        for result in results:
            match = True
            for key, value in filters.items():
                if key in result.metadata:
                    if result.metadata[key] != value:
                        match = False
                        break
            
            if match:
                filtered_results.append(result)
        
        logger.debug(f"过滤完成: {len(filtered_results)}/{len(results)}个结果通过")
        return filtered_results
    
    async def _rerank_results(self, results: List[RetrievalResult], 
                            query: str) -> List[RetrievalResult]:
        """重排序结果"""
        # 简单的重排序实现
        # 生产环境应使用专门的重排序模型
        
        # 基于内容长度和原始分数的简单重排序
        for result in results:
            content_length_score = min(len(result.content) / 1000, 1.0)
            result.score = result.score * 0.8 + content_length_score * 0.2
        
        # 重新排序
        reranked_results = sorted(results, key=lambda x: x.score, reverse=True)
        
        logger.debug(f"重排序完成: {len(reranked_results)}个结果")
        return reranked_results
