"""
知识库管理模块 - 文档处理、向量化、索引管理
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import hashlib
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

from models.document import Document, DocumentMetadata, DocumentChunk
from utils.logger import get_logger
from utils.text_processing import TextProcessor
from .storage.database import DatabaseManager
from .storage.vector_store import VectorStoreManager
from .ai_models.model_manager import ModelManager

logger = get_logger(__name__)


@dataclass
class ProcessingResult:
    """文档处理结果"""
    success: bool
    document_id: str
    chunks_count: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class VectorIndex:
    """向量索引"""
    document_id: str
    chunk_id: str
    vector: List[float]
    metadata: Dict[str, Any]


class DocumentProcessor:
    """文档处理器基类"""
    
    def __init__(self):
        self.text_processor = TextProcessor()
    
    async def process(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """
        处理文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[str, Dict]: (文档内容, 元数据)
        """
        raise NotImplementedError
    
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """提取文档元数据"""
        file_stat = os.stat(file_path)
        return {
            'file_name': os.path.basename(file_path),
            'file_size': file_stat.st_size,
            'created_time': datetime.fromtimestamp(file_stat.st_ctime),
            'modified_time': datetime.fromtimestamp(file_stat.st_mtime),
            'file_extension': Path(file_path).suffix.lower()
        }


class PDFProcessor(DocumentProcessor):
    """PDF文档处理器"""
    
    async def process(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """处理PDF文档"""
        try:
            # 这里应该使用实际的PDF处理库，如PyPDF2或pdfplumber
            # 暂时使用模拟实现
            content = f"PDF文档内容: {os.path.basename(file_path)}"
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'document_type': 'pdf',
                'page_count': 10,  # 模拟页数
                'language': 'zh-CN'
            })
            
            logger.debug(f"PDF处理完成: {file_path}")
            return content, metadata
            
        except Exception as e:
            logger.error(f"PDF处理失败: {file_path}, 错误: {str(e)}")
            raise


class TextProcessor(DocumentProcessor):
    """文本文档处理器"""
    
    async def process(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """处理文本文档"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'document_type': 'text',
                'character_count': len(content),
                'line_count': content.count('\n') + 1,
                'language': 'zh-CN'
            })
            
            logger.debug(f"文本处理完成: {file_path}")
            return content, metadata
            
        except Exception as e:
            logger.error(f"文本处理失败: {file_path}, 错误: {str(e)}")
            raise


class DocxProcessor(DocumentProcessor):
    """Word文档处理器"""
    
    async def process(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """处理Word文档"""
        try:
            # 这里应该使用python-docx库
            # 暂时使用模拟实现
            content = f"Word文档内容: {os.path.basename(file_path)}"
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'document_type': 'docx',
                'paragraph_count': 20,  # 模拟段落数
                'language': 'zh-CN'
            })
            
            logger.debug(f"Word处理完成: {file_path}")
            return content, metadata
            
        except Exception as e:
            logger.error(f"Word处理失败: {file_path}, 错误: {str(e)}")
            raise


class KnowledgeManager:
    """
    知识库管理器 - 负责文档摄取、处理、向量化和索引管理
    
    主要职责：
    1. 文档摄取和预处理
    2. 文本分割和向量化
    3. 知识图谱构建
    4. 索引管理和优化
    """
    
    def __init__(self):
        self.text_processor = TextProcessor()

        # 文档处理器映射
        self.processors = {
            '.pdf': PDFProcessor(),
            '.txt': TextProcessor(),
            '.docx': DocxProcessor(),
            '.doc': DocxProcessor()
        }

        # 数据库管理器
        self.db_manager = DatabaseManager()
        self.vector_store = VectorStoreManager()
        self.model_manager = ModelManager()

        # 内存缓存（用于快速访问）
        self.documents: Dict[str, Document] = {}
        self.document_chunks: Dict[str, List[DocumentChunk]] = {}

        # 配置参数
        self.chunk_size = 1000  # 文本块大小
        self.chunk_overlap = 200  # 文本块重叠
        self.max_chunks_per_doc = 100  # 每个文档最大块数

        logger.info("知识库管理器初始化完成")
    
    async def initialize(self) -> bool:
        """初始化知识库管理器"""
        try:
            # 连接数据库
            db_success = await self.db_manager.connect()
            vector_success = await self.vector_store.connect()
            model_success = await self.model_manager.initialize()

            if not db_success:
                logger.warning("数据库连接失败，部分功能可能受限")

            if not vector_success:
                logger.warning("向量存储连接失败，检索功能可能受限")

            if not model_success:
                logger.warning("AI模型初始化失败，将使用模拟向量化")

            logger.info("知识库管理器初始化完成")
            return True

        except Exception as e:
            logger.error(f"知识库管理器初始化失败: {e}")
            return False

    async def shutdown(self):
        """关闭知识库管理器"""
        try:
            await self.db_manager.disconnect()
            await self.vector_store.disconnect()
            await self.model_manager.shutdown()
            logger.info("知识库管理器已关闭")
        except Exception as e:
            logger.error(f"关闭知识库管理器失败: {e}")

    async def add_document(self, file_path: str, metadata: Optional[Dict] = None) -> bool:
        """
        添加文档到知识库

        Args:
            file_path: 文档路径
            metadata: 额外元数据

        Returns:
            bool: 是否成功
        """
        start_time = datetime.utcnow()

        try:
            logger.info(f"开始处理文档: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 生成文档ID
            document_id = self._generate_document_id(file_path)

            # 检查是否已存在
            existing_doc = await self.db_manager.get_document(document_id)
            if existing_doc:
                logger.warning(f"文档已存在: {document_id}")
                return True

            # 获取文档处理器
            file_extension = Path(file_path).suffix.lower()
            processor = self.processors.get(file_extension)
            if not processor:
                raise ValueError(f"不支持的文件类型: {file_extension}")

            # 保存文档信息到数据库
            filename = os.path.basename(file_path)
            await self.db_manager.save_document(document_id, filename, file_path, metadata or {})

            # 处理文档
            content, doc_metadata = await processor.process(file_path)

            # 合并元数据
            if metadata:
                doc_metadata.update(metadata)

            # 创建文档对象
            document = Document(
                id=document_id,
                content=content,
                metadata=DocumentMetadata(**doc_metadata)
            )

            # 文本分割
            chunks = await self._split_document(document)

            # 向量化和存储
            await self._vectorize_and_store_chunks(chunks)

            # 保存文档块到数据库
            chunk_data = [
                {
                    'id': chunk.id,
                    'document_id': chunk.document_id,
                    'chunk_index': chunk.metadata.get('chunk_index', 0),
                    'content': chunk.content,
                    'start_index': chunk.start_index,
                    'end_index': chunk.end_index,
                    'metadata': chunk.metadata
                }
                for chunk in chunks
            ]
            await self.db_manager.save_document_chunks(chunk_data)

            # 更新文档状态
            await self.db_manager.update_document_status(document_id, 'completed')

            # 缓存到内存
            self.documents[document_id] = document
            self.document_chunks[document_id] = chunks

            processing_time = (datetime.utcnow() - start_time).total_seconds()

            logger.info(f"文档处理完成: {document_id}, "
                       f"生成{len(chunks)}个文本块, 耗时: {processing_time:.2f}s")

            return True

        except Exception as e:
            logger.error(f"文档处理失败: {file_path}, 错误: {str(e)}")
            # 更新文档状态为失败
            if 'document_id' in locals():
                await self.db_manager.update_document_status(document_id, 'failed')
            return False
    
    async def remove_document(self, document_id: str) -> bool:
        """
        从知识库移除文档

        Args:
            document_id: 文档ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查文档是否存在
            existing_doc = await self.db_manager.get_document(document_id)
            if not existing_doc:
                logger.warning(f"文档不存在: {document_id}")
                return False

            # 获取文档块ID列表
            chunks = self.document_chunks.get(document_id, [])
            chunk_ids = [chunk.id for chunk in chunks]

            # 从向量存储删除
            if chunk_ids:
                await self.vector_store.delete_documents(chunk_ids)

            # 从数据库删除
            await self.db_manager.delete_document(document_id)

            # 从内存缓存移除
            if document_id in self.documents:
                del self.documents[document_id]
            if document_id in self.document_chunks:
                del self.document_chunks[document_id]

            logger.info(f"文档移除完成: {document_id}")
            return True

        except Exception as e:
            logger.error(f"文档移除失败: {document_id}, 错误: {str(e)}")
            return False
    
    async def get_document(self, document_id: str) -> Optional[Document]:
        """获取文档"""
        return self.documents.get(document_id)
    
    async def get_document_chunks(self, document_id: str) -> List[DocumentChunk]:
        """获取文档块"""
        return self.document_chunks.get(document_id, [])
    
    async def search_documents(self, query: str, top_k: int = 10,
                             query_embedding: Optional[List[float]] = None) -> List[DocumentChunk]:
        """
        搜索文档

        Args:
            query: 查询文本
            top_k: 返回结果数量
            query_embedding: 查询向量（可选）

        Returns:
            List[DocumentChunk]: 匹配的文档块
        """
        try:
            if query_embedding:
                # 使用向量相似度搜索
                vector_results = await self.vector_store.search_similar(
                    query_embedding, top_k
                )

                # 转换为DocumentChunk对象
                results = []
                for result in vector_results:
                    chunk = DocumentChunk(
                        id=result['id'],
                        document_id=result['metadata'].get('document_id', ''),
                        content=result['content'],
                        start_index=result['metadata'].get('start_index', 0),
                        end_index=result['metadata'].get('end_index', 0),
                        metadata=result['metadata']
                    )
                    results.append(chunk)

                return results
            else:
                # 简单的文本匹配搜索（后备方案）
                results = []

                for chunks in self.document_chunks.values():
                    for chunk in chunks:
                        if query.lower() in chunk.content.lower():
                            results.append(chunk)

                # 按相关性排序（简单实现）
                results.sort(key=lambda x: x.content.lower().count(query.lower()), reverse=True)

                return results[:top_k]

        except Exception as e:
            logger.error(f"文档搜索失败: {str(e)}")
            return []
    
    async def get_document_count(self) -> int:
        """获取文档数量"""
        return len(self.documents)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        total_chunks = sum(len(chunks) for chunks in self.document_chunks.values())
        total_vectors = len(self.vector_index)
        
        return {
            'document_count': len(self.documents),
            'chunk_count': total_chunks,
            'vector_count': total_vectors,
            'supported_formats': list(self.processors.keys())
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查基本功能
            stats = await self.get_statistics()
            return True
        except Exception as e:
            logger.error(f"知识库健康检查失败: {str(e)}")
            return False
    
    def _generate_document_id(self, file_path: str) -> str:
        """生成文档ID"""
        # 使用文件路径和修改时间生成唯一ID
        file_stat = os.stat(file_path)
        content = f"{file_path}_{file_stat.st_mtime}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def _split_document(self, document: Document) -> List[DocumentChunk]:
        """分割文档为文本块"""
        content = document.content
        chunks = []
        
        # 简单的文本分割实现
        # 生产环境应使用更智能的分割策略
        start = 0
        chunk_index = 0
        
        while start < len(content) and chunk_index < self.max_chunks_per_doc:
            end = min(start + self.chunk_size, len(content))
            
            # 尝试在句号处分割
            if end < len(content):
                last_period = content.rfind('。', start, end)
                if last_period > start:
                    end = last_period + 1
            
            chunk_content = content[start:end].strip()
            if chunk_content:
                chunk_id = f"{document.id}_chunk_{chunk_index}"
                chunk = DocumentChunk(
                    id=chunk_id,
                    document_id=document.id,
                    content=chunk_content,
                    start_index=start,
                    end_index=end,
                    metadata={
                        'chunk_index': chunk_index,
                        'chunk_size': len(chunk_content)
                    }
                )
                chunks.append(chunk)
                chunk_index += 1
            
            start = max(start + self.chunk_size - self.chunk_overlap, end)
        
        logger.debug(f"文档分割完成: {document.id}, 生成{len(chunks)}个块")
        return chunks
    
    async def _vectorize_and_store_chunks(self, chunks: List[DocumentChunk]):
        """向量化文本块并存储到向量数据库"""
        try:
            # 准备文本列表
            texts = [chunk.content for chunk in chunks]

            # 调用嵌入模型
            embedding_response = await self.model_manager.create_embeddings(texts)

            if embedding_response.error:
                logger.warning(f"嵌入生成失败，使用模拟向量: {embedding_response.error}")
                embeddings = [self._generate_mock_embedding(text) for text in texts]
            else:
                embeddings = embedding_response.embeddings

            # 准备向量化数据
            documents_to_vectorize = []

            for i, chunk in enumerate(chunks):
                doc_data = {
                    'id': chunk.id,
                    'content': chunk.content,
                    'embedding': embeddings[i],
                    'metadata': {
                        'document_id': chunk.document_id,
                        'chunk_index': chunk.metadata.get('chunk_index', 0),
                        'start_index': chunk.start_index,
                        'end_index': chunk.end_index,
                        'content_length': len(chunk.content)
                    }
                }
                documents_to_vectorize.append(doc_data)

            # 批量添加到向量存储
            await self.vector_store.add_documents(documents_to_vectorize)

            logger.debug(f"向量化和存储完成: {len(chunks)}个文本块")

        except Exception as e:
            logger.error(f"向量化和存储失败: {e}")
            raise

    def _generate_mock_embedding(self, text: str) -> List[float]:
        """生成模拟向量（开发环境使用）"""
        # 基于文本内容生成简单的模拟向量
        import hashlib

        # 使用文本哈希生成确定性的模拟向量
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # 生成768维向量
        embedding = []
        for i in range(768):
            # 使用哈希值的不同部分生成向量分量
            hash_part = text_hash[(i % len(text_hash))]
            value = (ord(hash_part) - 48) / 10.0 - 0.5  # 归一化到[-0.5, 0.5]
            embedding.append(value)

        return embedding
