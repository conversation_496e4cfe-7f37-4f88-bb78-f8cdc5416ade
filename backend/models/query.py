"""
查询相关数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class QueryType(str, Enum):
    """查询类型枚举"""
    GENERAL = "general"
    RISK_ASSESSMENT = "risk_assessment"
    COMPLIANCE_CHECK = "compliance_check"
    CLAUSE_ANALYSIS = "clause_analysis"
    COMPARISON = "comparison"
    SUMMARY = "summary"


class QueryStatus(str, Enum):
    """查询状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class QueryFilters(BaseModel):
    """查询过滤器"""
    document_types: Optional[List[str]] = None
    date_range: Optional[List[str]] = None  # [start_date, end_date]
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    authors: Optional[List[str]] = None
    languages: Optional[List[str]] = None
    custom_filters: Dict[str, Any] = Field(default_factory=dict)


class QueryRequest(BaseModel):
    """查询请求"""
    query: str = Field(..., min_length=1, max_length=1000)
    query_type: QueryType = QueryType.GENERAL
    document_id: Optional[str] = None
    filters: Optional[QueryFilters] = None
    
    # 检索参数
    top_k: int = Field(default=10, ge=1, le=50)
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    enable_rerank: bool = True
    
    # 生成参数
    model: Optional[str] = None
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, ge=1, le=4000)
    include_citations: bool = True
    
    # 其他参数
    language: str = "zh-CN"
    user_id: Optional[str] = None
    session_id: Optional[str] = None


class Query(BaseModel):
    """查询模型"""
    query_id: str
    query: str
    query_type: QueryType
    status: QueryStatus = QueryStatus.PENDING
    
    # 请求参数
    request_params: Dict[str, Any] = Field(default_factory=dict)
    
    # 用户信息
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 处理信息
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def start_processing(self):
        """开始处理"""
        self.status = QueryStatus.PROCESSING
        self.started_at = datetime.utcnow()
    
    def complete_processing(self, processing_time: float):
        """完成处理"""
        self.status = QueryStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.processing_time = processing_time
    
    def fail_processing(self, error_message: str):
        """处理失败"""
        self.status = QueryStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        if self.started_at:
            self.processing_time = (datetime.utcnow() - self.started_at).total_seconds()


class QueryResponse(BaseModel):
    """查询响应"""
    query_id: str
    query: str
    status: QueryStatus
    
    # 响应内容
    answer: Optional[str] = None
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    citations: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 元信息
    processing_time: Optional[float] = None
    model_used: Optional[str] = None
    tokens_used: Optional[int] = None
    
    # 质量指标
    confidence_score: Optional[float] = None
    relevance_score: Optional[float] = None
    hallucination_score: Optional[float] = None
    
    # 时间戳
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    # 错误信息
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
