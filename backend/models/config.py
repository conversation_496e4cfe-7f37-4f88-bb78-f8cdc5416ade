"""
RAG系统配置模型
"""

from typing import Optional, Dict, Any
from dataclasses import dataclass, field


@dataclass
class RAGConfig:
    """RAG系统配置"""
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 3600
    cache_max_size: int = 10000
    
    # 检索配置
    top_k: int = 5
    similarity_threshold: float = 0.7
    max_context_length: int = 2000
    
    # AI模型配置
    model_name: str = "qwen-turbo"
    embedding_model: str = "text-embedding-v1"
    max_tokens: int = 2000
    temperature: float = 0.7
    
    # 数据库配置
    database_url: Optional[str] = None
    vector_db_url: Optional[str] = None
    redis_url: Optional[str] = None
    
    # 系统配置
    debug_mode: bool = False
    enable_monitoring: bool = True
    log_level: str = "INFO"
    
    # 处理配置
    chunk_size: int = 500
    chunk_overlap: int = 50
    max_concurrent_requests: int = 10
    
    # API配置
    api_timeout: int = 30
    rate_limit: int = 100
    
    # 其他配置
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        # 验证配置
        if self.top_k <= 0:
            self.top_k = 5
        
        if self.cache_ttl <= 0:
            self.cache_ttl = 3600
        
        if self.similarity_threshold < 0 or self.similarity_threshold > 1:
            self.similarity_threshold = 0.7
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'enable_cache': self.enable_cache,
            'cache_ttl': self.cache_ttl,
            'cache_max_size': self.cache_max_size,
            'top_k': self.top_k,
            'similarity_threshold': self.similarity_threshold,
            'max_context_length': self.max_context_length,
            'model_name': self.model_name,
            'embedding_model': self.embedding_model,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'database_url': self.database_url,
            'vector_db_url': self.vector_db_url,
            'redis_url': self.redis_url,
            'debug_mode': self.debug_mode,
            'enable_monitoring': self.enable_monitoring,
            'log_level': self.log_level,
            'chunk_size': self.chunk_size,
            'chunk_overlap': self.chunk_overlap,
            'max_concurrent_requests': self.max_concurrent_requests,
            'api_timeout': self.api_timeout,
            'rate_limit': self.rate_limit,
            'metadata': self.metadata,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RAGConfig':
        """从字典创建配置"""
        return cls(**data)
    
    def update(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.metadata[key] = value
