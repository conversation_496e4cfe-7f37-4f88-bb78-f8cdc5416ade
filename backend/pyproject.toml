[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "rag-system"
version = "1.0.0"
description = "检索增强生成(RAG)系统"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "RAG Team", email = "<EMAIL>"}
]
keywords = ["rag", "retrieval", "generation", "ai", "nlp"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "httpx>=0.24.0",
    "asyncpg>=0.28.0",
    "redis>=4.5.0",
    "numpy>=1.24.0",
    "jieba>=0.42.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-html>=3.1.0",
    "pytest-xdist>=3.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
ai = [
    "openai>=1.0.0",
    "chromadb>=0.4.0",
]
docs = [
    "PyPDF2>=3.0.0",
    "python-docx>=0.8.11",
    "openpyxl>=3.1.0",
]

[project.scripts]
rag-server = "main:main"
rag-test = "run_tests:main"

[project.urls]
Homepage = "https://github.com/your-org/rag-system"
Documentation = "https://rag-system.readthedocs.io/"
Repository = "https://github.com/your-org/rag-system"
"Bug Tracker" = "https://github.com/your-org/rag-system/issues"

[tool.setuptools]
packages = ["core", "models", "utils", "config", "api", "tests"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--tb=short",
    "--disable-warnings",
    "--color=yes",
    "-ra"
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: 单元测试",
    "integration: 集成测试",
    "slow: 慢速测试",
    "api: API测试",
    "cache: 缓存相关测试",
    "database: 数据库相关测试",
    "ai_model: AI模型相关测试",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["core", "models", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "jieba.*",
    "chromadb.*",
    "redis.*",
]
ignore_missing_imports = true
