# RAG Backend 环境配置模板
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础服务配置
# =============================================================================

# 调试模式 (true/false)
DEBUG=false

# 服务监听地址
HOST=0.0.0.0

# 服务监听端口
PORT=8000

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 工作进程数 (生产环境建议设置为CPU核心数的2倍)
WORKERS=1

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL数据库连接URL (可选)
# 格式: postgresql://username:password@host:port/database
POSTGRES_URL=postgresql://rag_user:password@localhost:5432/rag_db

# Redis缓存连接URL (可选)
# 格式: redis://[:password@]host:port/db
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# AI模型配置
# =============================================================================

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# 千问API配置
QWEN_API_KEY=your-qwen-api-key-here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 默认使用的模型
DEFAULT_MODEL_PROVIDER=qwen

# =============================================================================
# 缓存配置
# =============================================================================

# 启用缓存 (true/false)
ENABLE_CACHE=true

# 缓存过期时间 (秒)
CACHE_TTL=3600

# 查询缓存过期时间 (秒)
QUERY_CACHE_TTL=1800

# =============================================================================
# 检索配置
# =============================================================================

# 默认检索文档数量
DEFAULT_TOP_K=5

# 最大检索文档数量
MAX_TOP_K=20

# 相似度阈值 (0.0-1.0)
SIMILARITY_THRESHOLD=0.7

# 最大上下文长度 (字符数)
MAX_CONTEXT_LENGTH=2000

# 文档分块大小 (字符数)
CHUNK_SIZE=1000

# 分块重叠大小 (字符数)
CHUNK_OVERLAP=200

# =============================================================================
# 安全配置
# =============================================================================

# JWT密钥 (生产环境请使用强随机密钥)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# CORS允许的源 (逗号分隔，* 表示允许所有)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 最大请求大小 (MB)
MAX_REQUEST_SIZE_MB=100

# 速率限制 (每分钟请求数)
RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# 监控配置
# =============================================================================

# 启用监控 (true/false)
ENABLE_MONITORING=true

# 启用指标收集 (true/false)
METRICS_ENABLED=true

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# 记录请求日志 (true/false)
LOG_REQUESTS=true

# =============================================================================
# 文件存储配置
# =============================================================================

# 上传文件存储路径
UPLOAD_PATH=./data/uploads

# 知识库存储路径
KNOWLEDGE_BASE_PATH=./data/knowledge_base

# 索引文件存储路径
INDEX_PATH=./data/indexes

# 日志文件存储路径
LOG_PATH=./logs

# =============================================================================
# 向量数据库配置 (可选)
# =============================================================================

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_COLLECTION_NAME=rag_documents

# =============================================================================
# 开发和调试配置
# =============================================================================

# 启用API文档 (true/false)
ENABLE_DOCS=true

# 测试模式 (true/false)
TESTING=false

# =============================================================================
# 注意事项
# =============================================================================

# 1. 生产环境请务必修改所有默认密钥和密码
# 2. 敏感信息建议使用环境变量或密钥管理服务
# 3. 根据实际需求调整缓存和数据库连接池配置
# 4. 定期检查和更新API密钥
# 5. 监控系统资源使用情况，及时调整配置
