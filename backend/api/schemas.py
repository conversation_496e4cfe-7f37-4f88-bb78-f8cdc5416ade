"""
API数据模式定义
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


# 请求模式
class DocumentUploadRequest(BaseModel):
    """文档上传请求"""
    filename: str
    document_type: str = "other"
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class QueryRequest(BaseModel):
    """查询请求"""
    query: str = Field(..., min_length=1, max_length=1000)
    query_type: str = "general"
    document_id: Optional[str] = None
    top_k: int = Field(default=10, ge=1, le=50)
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    include_citations: bool = True
    model: Optional[str] = None
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)


class KnowledgeBaseBuildRequest(BaseModel):
    """知识库构建请求"""
    source_path: str
    rebuild: bool = False
    document_types: Optional[List[str]] = None


# 响应模式
class DocumentResponse(BaseModel):
    """文档响应"""
    document_id: str
    filename: str
    document_type: str
    status: str
    created_at: datetime
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QueryResponse(BaseModel):
    """查询响应"""
    query_id: str
    query: str
    answer: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    citations: List[Dict[str, Any]] = Field(default_factory=list)
    processing_time: float
    confidence_score: Optional[float] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "1.0.0"
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseModel):
    """错误响应"""
    error_code: str
    error_message: str
    error_type: str = "internal_error"
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# 分页响应
class PaginatedResponse(BaseModel):
    """分页响应基类"""
    items: List[Any]
    total: int
    page: int = 1
    page_size: int = 10
    total_pages: int
    
    @classmethod
    def create(cls, items: List[Any], total: int, page: int = 1, page_size: int = 10):
        """创建分页响应"""
        total_pages = (total + page_size - 1) // page_size
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )


class DocumentListResponse(PaginatedResponse):
    """文档列表响应"""
    items: List[DocumentResponse]


# 统计响应
class StatsResponse(BaseModel):
    """统计响应"""
    total_documents: int
    total_queries: int
    avg_processing_time: float
    success_rate: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
