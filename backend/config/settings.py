"""
RAG系统配置管理
"""

import os
from typing import List, Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    try:
        from pydantic import BaseSettings, Field
    except ImportError:
        # 如果都没有，创建一个简单的替代
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        def Field(default=None, **kwargs):
            return default
from functools import lru_cache


class RAGSettings(BaseSettings):
    """RAG系统配置"""
    
    # 服务配置
    host: str = Field(default="0.0.0.0", env="RAG_HOST")
    port: int = Field(default=8000, env="RAG_PORT")
    debug: bool = Field(default=False, env="RAG_DEBUG")
    log_level: str = Field(default="INFO", env="RAG_LOG_LEVEL")
    log_file: Optional[str] = Field(default="./logs/rag.log", env="RAG_LOG_FILE")
    
    # 数据库配置
    postgres_url: str = Field(default="postgresql://localhost:5432/rag_db", env="POSTGRES_URL")
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    chroma_host: str = Field(default="localhost", env="CHROMA_HOST")
    chroma_port: int = Field(default=8000, env="CHROMA_PORT")
    
    # AI模型配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    qwen_api_key: Optional[str] = Field(default=None, env="QWEN_API_KEY")
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/api/v1", env="QWEN_BASE_URL")
    
    # 向量化配置
    embedding_model: str = Field(default="text-embedding-3-large", env="EMBEDDING_MODEL")
    embedding_dimensions: int = Field(default=1536, env="EMBEDDING_DIMENSIONS")
    embedding_batch_size: int = Field(default=100, env="EMBEDDING_BATCH_SIZE")
    
    # 检索配置
    retrieval_top_k: int = Field(default=10, env="RETRIEVAL_TOP_K")
    similarity_threshold: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    enable_rerank: bool = Field(default=True, env="ENABLE_RERANK")
    rerank_model: str = Field(default="bge-reranker-large", env="RERANK_MODEL")
    
    # 生成配置
    max_context_length: int = Field(default=8000, env="MAX_CONTEXT_LENGTH")
    default_llm_model: str = Field(default="gpt-4-turbo", env="DEFAULT_LLM_MODEL")
    enable_hallucination_detection: bool = Field(default=True, env="ENABLE_HALLUCINATION_DETECTION")
    enable_citation_generation: bool = Field(default=True, env="ENABLE_CITATION_GENERATION")
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # 监控配置
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # 安全配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="CORS_ORIGINS"
    )
    cors_methods: List[str] = Field(default=["GET", "POST", "PUT", "DELETE"])
    jwt_secret: Optional[str] = Field(default="dev-secret-key-change-in-production", env="JWT_SECRET")
    jwt_expire_time: int = Field(default=86400, env="JWT_EXPIRE_TIME")
    
    # 文件上传配置
    upload_max_size: int = Field(default=104857600, env="UPLOAD_MAX_SIZE")  # 100MB
    upload_allowed_types: List[str] = Field(
        default=["pdf", "docx", "doc", "txt", "html"],
        env="UPLOAD_ALLOWED_TYPES"
    )
    upload_path: str = Field(default="./data/uploads", env="UPLOAD_PATH")
    
    # 知识库配置
    knowledge_base_path: str = Field(default="./data/knowledge_base", env="KNOWLEDGE_BASE_PATH")
    index_path: str = Field(default="./data/indexes", env="INDEX_PATH")
    embeddings_path: str = Field(default="./data/embeddings", env="EMBEDDINGS_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def validate(self) -> bool:
        """验证配置"""
        errors = []

        # 检查必需的API密钥（开发环境可以跳过）
        if not self.debug and not self.openai_api_key and not self.qwen_api_key:
            errors.append("生产环境至少需要配置一个AI模型的API密钥")

        # 检查数据库连接（开发环境可以使用默认值）
        if not self.debug and not self.postgres_url:
            errors.append("生产环境POSTGRES_URL配置不能为空")

        if not self.debug and not self.redis_url:
            errors.append("生产环境REDIS_URL配置不能为空")
        
        # 检查目录权限
        directories = [
            self.upload_path,
            self.knowledge_base_path,
            self.index_path,
            self.embeddings_path
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目录 {directory}: {e}")
        
        if errors:
            for error in errors:
                print(f"配置错误: {error}")
            return False
        
        return True
    
    @property
    def chroma_url(self) -> str:
        """ChromaDB连接URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.debug
    
    @property
    def cors_origins_list(self) -> List[str]:
        """CORS允许的源列表"""
        if isinstance(self.cors_origins, str):
            return [origin.strip() for origin in self.cors_origins.split(",")]
        return self.cors_origins
    
    @property
    def upload_allowed_types_list(self) -> List[str]:
        """允许上传的文件类型列表"""
        if isinstance(self.upload_allowed_types, str):
            return [ext.strip() for ext in self.upload_allowed_types.split(",")]
        return self.upload_allowed_types


@lru_cache()
def get_settings() -> RAGSettings:
    """获取配置实例（单例模式）"""
    return RAGSettings()
