version: '3.8'

services:
  # RAG系统主服务
  rag-system:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: rag-system
    ports:
      - "8001:8001"
    environment:
      - RAG_ENV=development
      - RAG_HOST=0.0.0.0
      - RAG_PORT=8001
      - REDIS_HOST=redis
      - POSTGRES_HOST=postgres
      - CHROMA_HOST=chroma
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
      - ../config:/app/config
    depends_on:
      - redis
      - postgres
      - chroma
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rag-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL数据库（元数据存储）
  postgres:
    image: postgres:15-alpine
    container_name: rag-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=rag_metadata
      - POSTGRES_USER=rag_user
      - POSTGRES_PASSWORD=rag_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - rag-network
    restart: unless-stopped

  # ChromaDB向量数据库
  chroma:
    image: chromadb/chroma:latest
    container_name: rag-chroma
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=8000
    networks:
      - rag-network
    restart: unless-stopped

  # Prometheus监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - rag-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: rag-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - rag-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Nginx负载均衡（生产环境）
  nginx:
    image: nginx:alpine
    container_name: rag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - rag-system
    networks:
      - rag-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis_data:
  postgres_data:
  chroma_data:
  prometheus_data:
  grafana_data:

networks:
  rag-network:
    driver: bridge
